# Development Guidelines for Finanze.Pro Web Application

This document provides guidelines and information for developers working on the Finanze.Pro web application.

## Build and Configuration

### Prerequisites
- Node.js (version specified in `.nvmrc`)
- pnpm (version 10.12.1 or compatible)

### Setup
1. Install dependencies:
   ```bash
   pnpm install
   ```

2. Start the development server:
   ```bash
   pnpm dev
   ```

3. Build for production:
   ```bash
   pnpm build
   ```

4. Preview the production build:
   ```bash
   pnpm preview
   ```

### API Client Generation
The project uses OpenAPI TypeScript for API client generation:

```bash
pnpm generate:client
```

## Testing

### Testing Framework
The project uses Vitest as the testing framework and @testing-library/react for testing React components and hooks.

### Running Tests
To run all tests:
```bash
pnpm test
```

### Writing Tests

#### Unit Tests for Utility Functions
Create test files in `__tests__` directories adjacent to the files being tested:

```typescript
// src/utils/__tests__/my-util.test.ts
import { describe, expect, it } from "vitest";
import { myFunction } from "../my-util";

describe("myFunction", () => {
  it("should work as expected", () => {
    expect(myFunction(1, 2)).toBe(3);
  });
});
```

#### React Component/Hook Tests
For testing React components or hooks, use @testing-library/react:

```typescript
// src/components/__tests__/my-component.test.tsx
import { render, screen } from "@testing-library/react";
import { describe, expect, it } from "vitest";
import { MyComponent } from "../my-component";

describe("MyComponent", () => {
  it("should render correctly", () => {
    render(<MyComponent />);
    expect(screen.getByText("Expected Text")).toBeInTheDocument();
  });
});
```

For testing hooks, use renderHook:

```typescript
// src/hooks/__tests__/my-hook.test.ts
import { renderHook } from "@testing-library/react";
import { describe, expect, it } from "vitest";
import { useMyHook } from "../my-hook";

describe("useMyHook", () => {
  it("should return expected values", () => {
    const { result } = renderHook(() => useMyHook());
    expect(result.current).toHaveProperty("someProperty");
  });
});
```

#### Mocking Dependencies
Use Vitest's mocking capabilities to mock dependencies:

```typescript
import { vi } from "vitest";

vi.mock("../dependency", () => ({
  someFunction: vi.fn(),
}));
```

### Test Environment
Note that React component tests require a DOM environment. If you encounter "document is not defined" errors, you may need to configure Vitest to use jsdom or happy-dom.

## Code Style and Development Practices

### Code Formatting
The project uses Prettier for code formatting with the following key settings:
- Line width: 120 characters
- Double quotes
- Semicolons required
- ES5 trailing commas

### Linting
ESLint is configured with TypeScript support and includes:
- React Hooks rules
- TanStack Query rules
- Custom TypeScript rules

### Editor Configuration
EditorConfig is used to maintain consistent coding styles:
- 2 spaces for indentation
- LF line endings
- UTF-8 encoding
- No trailing whitespace
- Final newline in files

### Import Order
Imports are automatically sorted using @ianvs/prettier-plugin-sort-imports with a specific order defined in .prettierrc.

### Project Structure
- `src/`: Source code
  - `api/`: API client and types
  - `components/`: Reusable UI components
  - `features/`: Feature-specific code
  - `routes/`: Application routes
  - `utils/`: Utility functions

### Development Server
The development server is configured to proxy API requests to http://127.0.0.1:5000.
