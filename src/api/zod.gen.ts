// This file is auto-generated by @hey-api/openapi-ts

import { z } from "zod";

export const zTokenResponse = z.object({
  accessToken: z.string(),
});

export const zCommonError = z.object({
  success: z.boolean().optional().default(false),
  message: z.string(),
  stack: z.string().optional(),
});

export const zValidationError = z.object({
  success: z.boolean().optional().default(false),
  errors: z.array(z.string()),
  message: z.string(),
});

export const zTokenRequest = z.object({
  email: z.string().email(),
  password: z.string().min(8),
});

/**
 * ISO 4217 currency code
 */
export const zCurrency = z.enum(["EUR", "PLN", "UAH", "USD"]);

export const zUser = z.object({
  id: z.string().uuid(),
  email: z.string().email(),
  name: z.union([z.string(), z.null()]),
  baseCurrency: zCurrency,
  isActive: z.boolean().optional(),
});

export const zUserCreateRequest = z.object({
  email: z.string().email(),
  password: z.string().min(8),
  name: z.union([z.string(), z.null()]),
  baseCurrency: zCurrency,
});

export const zAccountGroup = z.object({
  id: z.string().uuid(),
  name: z.string(),
  color: z.union([z.string(), z.null()]),
  iconUrl: z.union([z.string(), z.null()]),
  createdAt: z.string().datetime(),
  updatedAt: z.string().datetime(),
});

export const zAccountGroupCreateRequest = z.object({
  name: z.string().min(1).max(100),
  color: z.string().optional(),
  iconUrl: z.string().url().optional(),
});

export const zAccountGroupUpdateRequest = z.object({
  name: z.string().min(1).max(100).optional(),
  color: z.string().optional(),
  iconUrl: z.string().url().optional(),
});

/**
 * Account type
 */
export const zAccountType = z.enum(["cash", "card", "bank_account", "savings", "loan", "other"]);

/**
 * Decimal number with up to 4 decimal places
 */
export const zDecimal = z.string().regex(/^(-|\+)?((\d+(\.\d*)?)|(\.\d+))$/);

export const zAccount = z.object({
  id: z.string().uuid(),
  groupId: z.union([z.string().uuid(), z.null()]),
  group: zAccountGroup.optional(),
  name: z.string(),
  currency: zCurrency,
  type: zAccountType,
  description: z.union([z.string(), z.null()]),
  color: z.union([z.string(), z.null()]),
  overdraftLimit: zDecimal,
  isActive: z.boolean(),
  openingBalance: zDecimal,
  currentBalance: zDecimal,
  baseOpeningBalance: zDecimal,
  baseCurrentBalance: zDecimal,
  createdAt: z.string().datetime(),
  updatedAt: z.string().datetime(),
});

export const zAccountCreateRequest = z.object({
  groupId: z.union([z.string().uuid(), z.null()]),
  name: z.string().min(3).max(100),
  currency: zCurrency,
  type: zAccountType,
  description: z.union([z.string(), z.null()]).optional(),
  color: z.union([z.string(), z.null()]).optional(),
  overdraftLimit: zDecimal.and(z.union([z.string(), z.null()])).optional(),
  openingBalance: zDecimal,
});

export const zAccountUpdateRequest = z.object({
  groupId: z.union([z.string().uuid(), z.null()]).optional(),
  name: z.string().min(3).max(100).optional(),
  currency: zCurrency.optional(),
  type: zAccountType.optional(),
  description: z.union([z.string(), z.null()]).optional(),
  color: z.union([z.string(), z.null()]).optional(),
  overdraftLimit: zDecimal.and(z.union([z.string(), z.null()])).optional(),
  isActive: z.boolean().optional(),
});

export const zAccountBalanceUpdateRequest = z.object({
  currentBalance: zDecimal,
});

/**
 * Budget period
 */
export const zBudgetPeriod = z.enum(["week", "month", "quarter", "year"]);

/**
 * Budget type
 */
export const zBudgetType = z.enum(["fixed", "percentage"]);

/**
 * Budget record for a specific or current period
 */
export const zBudgetRecord = z.object({
  id: z.string().uuid(),
  budgetId: z.string().uuid(),
  startDate: z.string().date(),
  endDate: z.string().date(),
  plannedAmount: zDecimal,
  usedAmount: zDecimal,
  createdAt: z.string().datetime(),
  updatedAt: z.string().datetime(),
});

export const zBudget = z.object({
  id: z.string().uuid(),
  name: z.string(),
  description: z.union([z.string(), z.null()]),
  period: zBudgetPeriod,
  type: zBudgetType,
  value: zDecimal,
  accounts: z.union([z.array(z.string().uuid()), z.null()]),
  isArchived: z.boolean(),
  currentRecord: zBudgetRecord.optional(),
  createdAt: z.string().datetime(),
  updatedAt: z.string().datetime(),
});

export const zBudgetListResponse = z.array(zBudget);

export const zBudgetCreateRequest = z.object({
  name: z.string().min(1).max(100),
  description: z.union([z.string(), z.null()]).optional(),
  period: zBudgetPeriod,
  type: zBudgetType,
  value: zDecimal,
  accounts: z.union([z.array(z.string().uuid()), z.null()]).optional(),
});

export const zBudgetUpdateRequest = z.object({
  name: z.string().min(1).max(100).optional(),
  description: z.union([z.string(), z.null()]).optional(),
  type: zBudgetType.optional(),
  value: zDecimal.optional(),
  accounts: z.union([z.array(z.string().uuid()), z.null()]).optional(),
  isArchived: z.boolean().optional(),
});

export const zPaginationMeta = z.object({
  total: z.number().int(),
  page: z.number().int(),
  limit: z.number().int(),
  count: z.number().int(),
});

export const zBudgetHistoryResponse = z.object({
  items: z.array(zBudgetRecord),
  meta: zPaginationMeta,
});

export const zCategory = z.object({
  id: z.string().uuid(),
  name: z.string(),
  color: z.union([z.string(), z.null()]),
  icon: z.union([z.string(), z.null()]),
  isExpense: z.boolean(),
  createdAt: z.string().datetime(),
  updatedAt: z.string().datetime(),
});

export const zCategoryCreateRequest = z.object({
  name: z.string().min(1).max(100),
  color: z
    .union([z.string().default("#ff6b6b"), z.null()])
    .optional()
    .default("#ff6b6b"),
  icon: z.union([z.string(), z.null()]).optional(),
  isExpense: z.boolean().optional().default(true),
});

export const zCategoryUpdateRequest = z.object({
  name: z.string().min(1).max(100).optional(),
  color: z.string().optional(),
  icon: z.string().optional(),
  isExpense: z.boolean().optional(),
});

export const zGoal = z.object({
  id: z.string().uuid(),
  name: z.string(),
  accounts: z.array(z.string().uuid()),
  targetAmount: zDecimal.and(z.union([z.string(), z.null()])),
  currentAmount: zDecimal.and(z.unknown()),
  description: z.union([z.string(), z.null()]),
  color: z.union([z.string(), z.null()]),
  iconUrl: z.union([z.string(), z.null()]),
  targetDate: z.union([z.string().date(), z.null()]),
  currency: zCurrency,
  createdAt: z.string().datetime(),
  updatedAt: z.string().datetime(),
});

export const zGoalCreateRequest = z.object({
  name: z.string().min(1).max(100),
  accounts: z.array(z.string().uuid()).min(1),
  targetAmount: zDecimal.optional(),
  description: z.string().optional(),
  color: z.string().optional(),
  iconUrl: z.string().url().optional(),
  targetDate: z.string().date().optional(),
});

export const zGoalUpdateRequest = z.object({
  name: z.string().min(1).max(100).optional(),
  accounts: z.array(z.string().uuid()).min(1).optional(),
  targetAmount: zDecimal.optional(),
  description: z.string().optional(),
  color: z.string().optional(),
  iconUrl: z.string().url().optional(),
  targetDate: z.string().date().optional(),
});

/**
 * Task recurrence period
 */
export const zTaskPeriod = z.enum(["weekly", "monthly", "quarterly", "yearly"]);

/**
 * Task record status
 */
export const zTaskStatus = z.enum(["active", "completed", "skipped"]);

/**
 * Current task record for this period
 */
export const zTaskRecordResponse = z.union([
  z.object({
    id: z.string().uuid(),
    taskId: z.string().uuid(),
    startDate: z.string().date(),
    endDate: z.string().date(),
    status: zTaskStatus,
    amount: zDecimal.and(z.union([z.string(), z.null()])),
    createdAt: z.string().datetime(),
    updatedAt: z.string().datetime(),
  }),
  z.null(),
]);

export const zTaskResponse = z.object({
  id: z.string().uuid(),
  title: z.string(),
  period: zTaskPeriod,
  description: z.union([z.string(), z.null()]),
  amount: zDecimal.and(z.union([z.string(), z.null()])),
  dueDay: z.union([z.number().int(), z.null()]),
  isArchived: z.boolean(),
  sendNotifications: z.boolean(),
  current: zTaskRecordResponse,
  createdAt: z.string().datetime(),
  updatedAt: z.string().datetime(),
});

export const zTaskListResponse = z.object({
  items: z.array(zTaskResponse),
  meta: zPaginationMeta,
});

export const zTaskCreateRequest = z.object({
  title: z.string().min(1).max(100),
  period: zTaskPeriod,
  description: z.union([z.string(), z.null()]).optional(),
  amount: zDecimal.and(z.union([z.string(), z.null()])).optional(),
  dueDay: z.union([z.number().int().gte(1).lte(31), z.null()]).optional(),
  sendNotifications: z.boolean().optional().default(false),
});

export const zTaskUpdateRequest = z.object({
  title: z.string().min(1).max(100).optional(),
  period: zTaskPeriod.optional(),
  description: z.string().optional(),
  amount: zDecimal.and(z.unknown()).optional(),
  dueDay: z.number().int().gte(1).lte(31).optional(),
  isArchived: z.boolean().optional(),
  sendNotifications: z.boolean().optional(),
});

export const zTaskStatusUpdateRequest = z.object({
  status: z.enum(["completed", "skipped"]),
  amount: zDecimal.and(z.unknown()).optional(),
});

export const zTaskHistoryResponse = z.object({
  items: z.array(z.union([zTaskRecordResponse, z.object({})])),
  meta: zPaginationMeta,
});

export const zTaskTransactionAssignment = z.object({
  transactions: z.array(z.string().uuid()),
});

/**
 * Transaction type
 */
export const zTransactionType = z.enum(["income", "expense", "transfer"]);

export const zTransactionResponse = z.object({
  id: z.string().uuid(),
  transactionDate: z.string().date(),
  categoryId: z.union([z.string().uuid(), z.null()]),
  category: zCategory.optional(),
  type: zTransactionType,
  description: z.union([z.string(), z.null()]),
  accountId: z.string().uuid(),
  account: zAccount,
  amount: zDecimal,
  accountToId: z.union([z.string().uuid(), z.null()]),
  accountTo: zAccount.optional(),
  amountTo: zDecimal.and(z.union([z.string(), z.null()])),
  baseAmount: zDecimal,
  baseAmountTo: zDecimal.and(z.union([z.string(), z.null()])),
  taskRecordId: z.union([z.string().uuid(), z.null()]),
  createdAt: z.string().datetime(),
  updatedAt: z.string().datetime(),
});

export const zTransactionListMeta = z.object({
  total: z.number().int().gte(0),
  page: z.number().int().gte(1),
  limit: z.number().int().gte(1),
  count: z.number().int().gte(0),
});

export const zTransactionListResponse = z.object({
  items: z.array(zTransactionResponse),
  meta: zTransactionListMeta,
});

export const zTransactionCreateRequest = z.object({
  transactionDate: z.string().date(),
  categoryId: z.union([z.string().uuid(), z.null()]).optional(),
  type: zTransactionType,
  description: z.union([z.string(), z.null()]).optional(),
  accountId: z.string().uuid(),
  amount: zDecimal,
  accountToId: z.union([z.string().uuid(), z.null()]).optional(),
  amountTo: zDecimal.and(z.union([z.string(), z.null()])).optional(),
});

export const zTransactionUpdateRequest = z.object({
  transactionDate: z.string().date().optional(),
  categoryId: z.union([z.string().uuid(), z.null()]).optional(),
  type: zTransactionType.optional(),
  description: z.union([z.string(), z.null()]).optional(),
  accountId: z.string().uuid().optional(),
  amount: zDecimal.optional(),
  accountToId: z.union([z.string().uuid(), z.null()]).optional(),
  amountTo: zDecimal.and(z.union([z.string(), z.null()])).optional(),
});

export const zGetTokenData = z.object({
  body: zTokenRequest,
  headers: z.never().optional(),
  path: z.never().optional(),
  query: z.never().optional(),
});

/**
 * Access token
 */
export const zGetTokenResponse = zTokenResponse;

export const zLogoutData = z.object({
  body: z.never().optional(),
  headers: z.never().optional(),
  path: z.never().optional(),
  query: z.never().optional(),
});

/**
 * Successfully logged out
 */
export const zLogoutResponse = z.void();

export const zCreateUserData = z.object({
  body: zUserCreateRequest,
  headers: z.never().optional(),
  path: z.never().optional(),
  query: z.never().optional(),
});

/**
 * User created
 */
export const zCreateUserResponse = zUser;

export const zGetCurrentUserData = z.object({
  body: z.never().optional(),
  headers: z.never().optional(),
  path: z.never().optional(),
  query: z.never().optional(),
});

/**
 * Current user
 */
export const zGetCurrentUserResponse = zUser;

export const zListAccountGroupsData = z.object({
  body: z.never().optional(),
  headers: z.never().optional(),
  path: z.never().optional(),
  query: z.never().optional(),
});

/**
 * Account groups retrieved
 */
export const zListAccountGroupsResponse = z.array(zAccountGroup);

export const zCreateAccountGroupData = z.object({
  body: zAccountGroupCreateRequest,
  headers: z.never().optional(),
  path: z.never().optional(),
  query: z.never().optional(),
});

/**
 * Account group created
 */
export const zCreateAccountGroupResponse = zAccountGroup;

export const zDeleteAccountGroupData = z.object({
  body: z.never().optional(),
  headers: z.never().optional(),
  path: z.object({
    id: z.string().uuid(),
  }),
  query: z.never().optional(),
});

/**
 * Account group deleted
 */
export const zDeleteAccountGroupResponse = z.void();

export const zGetAccountGroupData = z.object({
  body: z.never().optional(),
  headers: z.never().optional(),
  path: z.object({
    id: z.string().uuid(),
  }),
  query: z.never().optional(),
});

/**
 * Account group retrieved
 */
export const zGetAccountGroupResponse = zAccountGroup;

export const zUpdateAccountGroupData = z.object({
  body: zAccountGroupUpdateRequest,
  headers: z.never().optional(),
  path: z.object({
    id: z.string().uuid(),
  }),
  query: z.never().optional(),
});

/**
 * Account group updated
 */
export const zUpdateAccountGroupResponse = zAccountGroup;

export const zListAccountsData = z.object({
  body: z.never().optional(),
  headers: z.never().optional(),
  path: z.never().optional(),
  query: z.never().optional(),
});

/**
 * Accounts retrieved
 */
export const zListAccountsResponse = z.array(zAccount);

export const zCreateAccountData = z.object({
  body: zAccountCreateRequest,
  headers: z.never().optional(),
  path: z.never().optional(),
  query: z.never().optional(),
});

/**
 * Account created
 */
export const zCreateAccountResponse = zAccount;

export const zDeleteAccountData = z.object({
  body: z.never().optional(),
  headers: z.never().optional(),
  path: z.object({
    id: z.string().uuid(),
  }),
  query: z.never().optional(),
});

/**
 * Account deleted
 */
export const zDeleteAccountResponse = z.void();

export const zGetAccountData = z.object({
  body: z.never().optional(),
  headers: z.never().optional(),
  path: z.object({
    id: z.string().uuid(),
  }),
  query: z.never().optional(),
});

/**
 * Account retrieved
 */
export const zGetAccountResponse = zAccount;

export const zUpdateAccountData = z.object({
  body: zAccountUpdateRequest,
  headers: z.never().optional(),
  path: z.object({
    id: z.string().uuid(),
  }),
  query: z.never().optional(),
});

/**
 * Account updated
 */
export const zUpdateAccountResponse = zAccount;

export const zUpdateAccountBalanceData = z.object({
  body: zAccountBalanceUpdateRequest,
  headers: z.never().optional(),
  path: z.object({
    id: z.string().uuid(),
  }),
  query: z.never().optional(),
});

/**
 * Account balance updated
 */
export const zUpdateAccountBalanceResponse = zAccount;

export const zListBudgetsData = z.object({
  body: z.never().optional(),
  headers: z.never().optional(),
  path: z.never().optional(),
  query: z
    .object({
      includeArchived: z
        .union([z.boolean().default(false), z.null()])
        .optional()
        .default(false),
    })
    .optional(),
});

/**
 * Budgets retrieved
 */
export const zListBudgetsResponse = zBudgetListResponse;

export const zCreateBudgetData = z.object({
  body: zBudgetCreateRequest,
  headers: z.never().optional(),
  path: z.never().optional(),
  query: z.never().optional(),
});

/**
 * Budget created
 */
export const zCreateBudgetResponse = zBudget;

export const zDeleteBudgetData = z.object({
  body: z.never().optional(),
  headers: z.never().optional(),
  path: z.object({
    id: z.string().uuid(),
  }),
  query: z.never().optional(),
});

/**
 * Budget deleted
 */
export const zDeleteBudgetResponse = z.void();

export const zGetBudgetData = z.object({
  body: z.never().optional(),
  headers: z.never().optional(),
  path: z.object({
    id: z.string().uuid(),
  }),
  query: z.never().optional(),
});

/**
 * Budget retrieved
 */
export const zGetBudgetResponse = zBudget;

export const zUpdateBudgetData = z.object({
  body: zBudgetUpdateRequest,
  headers: z.never().optional(),
  path: z.object({
    id: z.string().uuid(),
  }),
  query: z.never().optional(),
});

/**
 * Budget updated
 */
export const zUpdateBudgetResponse = zBudget;

export const zGetBudgetHistoryData = z.object({
  body: z.never().optional(),
  headers: z.never().optional(),
  path: z.object({
    id: z.string().uuid(),
  }),
  query: z
    .object({
      page: z.number().int().gte(1).optional().default(1),
      limit: z.number().int().gte(1).lte(100).optional().default(20),
    })
    .optional(),
});

/**
 * Budget history retrieved
 */
export const zGetBudgetHistoryResponse = zBudgetHistoryResponse;

export const zListCategoriesData = z.object({
  body: z.never().optional(),
  headers: z.never().optional(),
  path: z.never().optional(),
  query: z.never().optional(),
});

/**
 * Categories retrieved
 */
export const zListCategoriesResponse = z.array(zCategory);

export const zCreateCategoryData = z.object({
  body: zCategoryCreateRequest,
  headers: z.never().optional(),
  path: z.never().optional(),
  query: z.never().optional(),
});

/**
 * Category created
 */
export const zCreateCategoryResponse = zCategory;

export const zDeleteCategoryData = z.object({
  body: z.never().optional(),
  headers: z.never().optional(),
  path: z.object({
    id: z.string().uuid(),
  }),
  query: z.never().optional(),
});

/**
 * Category deleted
 */
export const zDeleteCategoryResponse = z.void();

export const zGetCategoryData = z.object({
  body: z.never().optional(),
  headers: z.never().optional(),
  path: z.object({
    id: z.string().uuid(),
  }),
  query: z.never().optional(),
});

/**
 * Category retrieved
 */
export const zGetCategoryResponse = zCategory;

export const zUpdateCategoryData = z.object({
  body: zCategoryUpdateRequest,
  headers: z.never().optional(),
  path: z.object({
    id: z.string().uuid(),
  }),
  query: z.never().optional(),
});

/**
 * Category updated
 */
export const zUpdateCategoryResponse = zCategory;

export const zListGoalsData = z.object({
  body: z.never().optional(),
  headers: z.never().optional(),
  path: z.never().optional(),
  query: z.never().optional(),
});

/**
 * Goals retrieved
 */
export const zListGoalsResponse = z.array(zGoal);

export const zCreateGoalData = z.object({
  body: zGoalCreateRequest,
  headers: z.never().optional(),
  path: z.never().optional(),
  query: z.never().optional(),
});

/**
 * Goal created
 */
export const zCreateGoalResponse = zGoal;

export const zDeleteGoalData = z.object({
  body: z.never().optional(),
  headers: z.never().optional(),
  path: z.object({
    id: z.string().uuid(),
  }),
  query: z.never().optional(),
});

/**
 * Goal deleted
 */
export const zDeleteGoalResponse = z.object({
  message: z.string(),
});

export const zGetGoalData = z.object({
  body: z.never().optional(),
  headers: z.never().optional(),
  path: z.object({
    id: z.string().uuid(),
  }),
  query: z.never().optional(),
});

/**
 * Goal retrieved
 */
export const zGetGoalResponse = zGoal;

export const zUpdateGoalData = z.object({
  body: zGoalUpdateRequest,
  headers: z.never().optional(),
  path: z.object({
    id: z.string().uuid(),
  }),
  query: z.never().optional(),
});

/**
 * Goal updated
 */
export const zUpdateGoalResponse = zGoal;

export const zListTasksData = z.object({
  body: z.never().optional(),
  headers: z.never().optional(),
  path: z.never().optional(),
  query: z
    .object({
      page: z.number().int().gte(1).optional().default(1),
      limit: z.number().int().gte(1).lte(100).optional().default(20),
    })
    .optional(),
});

/**
 * Tasks retrieved
 */
export const zListTasksResponse = zTaskListResponse;

export const zCreateTaskData = z.object({
  body: zTaskCreateRequest,
  headers: z.never().optional(),
  path: z.never().optional(),
  query: z.never().optional(),
});

/**
 * Task created
 */
export const zCreateTaskResponse = zTaskResponse;

export const zDeleteTaskData = z.object({
  body: z.never().optional(),
  headers: z.never().optional(),
  path: z.object({
    id: z.string().uuid(),
  }),
  query: z.never().optional(),
});

/**
 * Task deleted
 */
export const zDeleteTaskResponse = z.void();

export const zGetTaskData = z.object({
  body: z.never().optional(),
  headers: z.never().optional(),
  path: z.object({
    id: z.string().uuid(),
  }),
  query: z.never().optional(),
});

/**
 * Task retrieved
 */
export const zGetTaskResponse = zTaskResponse;

export const zUpdateTaskData = z.object({
  body: zTaskUpdateRequest,
  headers: z.never().optional(),
  path: z.object({
    id: z.string().uuid(),
  }),
  query: z.never().optional(),
});

/**
 * Task updated
 */
export const zUpdateTaskResponse = zTaskResponse;

export const zUpdateTaskStatusData = z.object({
  body: zTaskStatusUpdateRequest,
  headers: z.never().optional(),
  path: z.object({
    id: z.string().uuid(),
  }),
  query: z.never().optional(),
});

export const zGetTaskHistoryData = z.object({
  body: z.never().optional(),
  headers: z.never().optional(),
  path: z.object({
    id: z.string().uuid(),
  }),
  query: z
    .object({
      page: z.number().int().gte(1).optional().default(1),
      limit: z.number().int().gte(1).lte(100).optional().default(20),
    })
    .optional(),
});

/**
 * Task history retrieved
 */
export const zGetTaskHistoryResponse = zTaskHistoryResponse;

export const zRemoveTaskTransactionsData = z.object({
  body: zTaskTransactionAssignment,
  headers: z.never().optional(),
  path: z.object({
    id: z.string().uuid(),
  }),
  query: z.never().optional(),
});

export const zAssignTaskTransactionsData = z.object({
  body: zTaskTransactionAssignment,
  headers: z.never().optional(),
  path: z.object({
    id: z.string().uuid(),
  }),
  query: z.never().optional(),
});

export const zReplaceTaskTransactionsData = z.object({
  body: zTaskTransactionAssignment,
  headers: z.never().optional(),
  path: z.object({
    id: z.string().uuid(),
  }),
  query: z.never().optional(),
});

export const zListTransactionsData = z.object({
  body: z.never().optional(),
  headers: z.never().optional(),
  path: z.never().optional(),
  query: z
    .object({
      page: z.number().int().gte(1).optional().default(1),
      limit: z.number().int().gte(1).lte(100).optional().default(20),
    })
    .optional(),
});

/**
 * Paginated list of transactions
 */
export const zListTransactionsResponse = zTransactionListResponse;

export const zCreateTransactionData = z.object({
  body: zTransactionCreateRequest,
  headers: z.never().optional(),
  path: z.never().optional(),
  query: z.never().optional(),
});

/**
 * Transaction created successfully
 */
export const zCreateTransactionResponse = zTransactionResponse;

export const zDeleteTransactionData = z.object({
  body: z.never().optional(),
  headers: z.never().optional(),
  path: z.object({
    id: z.string().uuid(),
  }),
  query: z.never().optional(),
});

/**
 * Transaction deleted successfully
 */
export const zDeleteTransactionResponse = z.void();

export const zGetTransactionData = z.object({
  body: z.never().optional(),
  headers: z.never().optional(),
  path: z.object({
    id: z.string().uuid(),
  }),
  query: z.never().optional(),
});

/**
 * Transaction details
 */
export const zGetTransactionResponse = zTransactionResponse;

export const zUpdateTransactionData = z.object({
  body: zTransactionUpdateRequest,
  headers: z.never().optional(),
  path: z.object({
    id: z.string().uuid(),
  }),
  query: z.never().optional(),
});

/**
 * Transaction updated successfully
 */
export const zUpdateTransactionResponse = zTransactionResponse;
