// This file is auto-generated by @hey-api/openapi-ts

export type TokenResponse = {
  /**
   * Access token
   */
  accessToken: string;
};

export type CommonError = {
  success?: boolean;
  message: string;
  stack?: string;
};

export type ValidationError = {
  success?: boolean;
  errors: Array<string>;
  message: string;
};

export type TokenRequest = {
  email: string;
  password: string;
};

/**
 * ISO 4217 currency code
 */
export type Currency = "EUR" | "PLN" | "UAH" | "USD";

export type User = {
  id: string;
  email: string;
  name: string | null;
  baseCurrency: Currency;
  isActive?: boolean;
};

export type UserCreateRequest = {
  email: string;
  password: string;
  name: string | null;
  baseCurrency: Currency;
};

export type AccountGroup = {
  id: string;
  name: string;
  color: string | null;
  iconUrl: string | null;
  createdAt: string;
  updatedAt: string;
};

export type AccountGroupCreateRequest = {
  name: string;
  color?: string;
  iconUrl?: string;
};

export type AccountGroupUpdateRequest = {
  name?: string;
  color?: string;
  iconUrl?: string;
};

/**
 * Account type
 */
export type AccountType = "cash" | "card" | "bank_account" | "savings" | "loan" | "other";

/**
 * Decimal number with up to 4 decimal places
 */
export type Decimal = string;

export type Account = {
  id: string;
  groupId: string | null;
  group?: AccountGroup;
  name: string;
  currency: Currency;
  type: AccountType;
  description: string | null;
  color: string | null;
  overdraftLimit: Decimal;
  isActive: boolean;
  openingBalance: Decimal;
  currentBalance: Decimal;
  baseOpeningBalance: Decimal;
  baseCurrentBalance: Decimal;
  createdAt: string;
  updatedAt: string;
};

export type AccountCreateRequest = {
  groupId: string | null;
  name: string;
  currency: Currency;
  type: AccountType;
  description?: string | null;
  color?: string | null;
  overdraftLimit?: Decimal & (string | null);
  openingBalance: Decimal;
};

export type AccountUpdateRequest = {
  groupId?: string | null;
  name?: string;
  currency?: Currency;
  type?: AccountType;
  description?: string | null;
  color?: string | null;
  overdraftLimit?: Decimal & (string | null);
  isActive?: boolean;
};

export type AccountBalanceUpdateRequest = {
  currentBalance: Decimal;
};

/**
 * Budget period
 */
export type BudgetPeriod = "week" | "month" | "quarter" | "year";

/**
 * Budget type
 */
export type BudgetType = "fixed" | "percentage";

/**
 * Budget record for a specific or current period
 */
export type BudgetRecord = {
  id: string;
  budgetId: string;
  startDate: string;
  endDate: string;
  plannedAmount: Decimal;
  usedAmount: Decimal;
  createdAt: string;
  updatedAt: string;
};

export type Budget = {
  id: string;
  name: string;
  description: string | null;
  period: BudgetPeriod;
  type: BudgetType;
  value: Decimal;
  /**
   * List of account IDs this budget is restricted to
   */
  accounts: Array<string> | null;
  isArchived: boolean;
  currentRecord?: BudgetRecord;
  createdAt: string;
  updatedAt: string;
};

export type BudgetListResponse = Array<Budget>;

export type BudgetCreateRequest = {
  name: string;
  description?: string | null;
  period: BudgetPeriod;
  type: BudgetType;
  value: Decimal;
  /**
   * Optional list of account IDs to restrict budget to
   */
  accounts?: Array<string> | null;
};

export type BudgetUpdateRequest = {
  name?: string;
  description?: string | null;
  type?: BudgetType;
  value?: Decimal;
  /**
   * Optional list of account IDs to restrict budget to
   */
  accounts?: Array<string> | null;
  isArchived?: boolean;
};

export type PaginationMeta = {
  /**
   * Total number of items
   */
  total: number;
  /**
   * Current page
   */
  page: number;
  /**
   * Items per page
   */
  limit: number;
  /**
   * Number of items on current page
   */
  count: number;
};

export type BudgetHistoryResponse = {
  items: Array<BudgetRecord>;
  meta: PaginationMeta;
};

export type Category = {
  id: string;
  name: string;
  color: string | null;
  icon: string | null;
  isExpense: boolean;
  createdAt: string;
  updatedAt: string;
};

export type CategoryCreateRequest = {
  name: string;
  color?: string | null;
  icon?: string | null;
  isExpense?: boolean;
};

export type CategoryUpdateRequest = {
  name?: string;
  color?: string;
  icon?: string;
  isExpense?: boolean;
};

export type Goal = {
  id: string;
  name: string;
  accounts: Array<string>;
  targetAmount: Decimal & (string | null);
  currentAmount: Decimal & unknown;
  description: string | null;
  color: string | null;
  iconUrl: string | null;
  targetDate: string | null;
  currency: Currency;
  createdAt: string;
  updatedAt: string;
};

export type GoalCreateRequest = {
  name: string;
  accounts: Array<string>;
  targetAmount?: Decimal;
  description?: string;
  color?: string;
  iconUrl?: string;
  targetDate?: string;
};

export type GoalUpdateRequest = {
  name?: string;
  accounts?: Array<string>;
  targetAmount?: Decimal;
  description?: string;
  color?: string;
  iconUrl?: string;
  targetDate?: string;
};

/**
 * Task recurrence period
 */
export type TaskPeriod = "weekly" | "monthly" | "quarterly" | "yearly";

/**
 * Task record status
 */
export type TaskStatus = "active" | "completed" | "skipped";

/**
 * Current task record for this period
 */
export type TaskRecordResponse = {
  id: string;
  taskId: string;
  startDate: string;
  endDate: string;
  status: TaskStatus;
  amount: Decimal & (string | null);
  createdAt: string;
  updatedAt: string;
} | null;

export type TaskResponse = {
  id: string;
  title: string;
  period: TaskPeriod;
  description: string | null;
  amount: Decimal & (string | null);
  dueDay: number | null;
  isArchived: boolean;
  sendNotifications: boolean;
  current: TaskRecordResponse;
  createdAt: string;
  updatedAt: string;
};

export type TaskListResponse = {
  items: Array<TaskResponse>;
  meta: PaginationMeta;
};

export type TaskCreateRequest = {
  title: string;
  period: TaskPeriod;
  description?: string | null;
  amount?: Decimal & (string | null);
  /**
   * Day of the period when task is due
   */
  dueDay?: number | null;
  sendNotifications?: boolean;
};

export type TaskUpdateRequest = {
  title?: string;
  period?: TaskPeriod;
  description?: string;
  amount?: Decimal & unknown;
  /**
   * Day of the period when task is due
   */
  dueDay?: number;
  isArchived?: boolean;
  sendNotifications?: boolean;
};

export type TaskStatusUpdateRequest = {
  /**
   * The new status for the current task record. Must be 'completed' or 'skipped'.
   */
  status: "completed" | "skipped";
  amount?: Decimal & unknown;
};

export type TaskHistoryResponse = {
  items: Array<
    TaskRecordResponse & {
      [key: string]: unknown;
    }
  >;
  meta: PaginationMeta;
};

export type TaskTransactionAssignment = {
  transactions: Array<string>;
};

/**
 * Transaction type
 */
export type TransactionType = "income" | "expense" | "transfer";

export type TransactionResponse = {
  id: string;
  transactionDate: string;
  categoryId: string | null;
  category?: Category;
  type: TransactionType;
  description: string | null;
  accountId: string;
  account: Account;
  amount: Decimal;
  accountToId: string | null;
  accountTo?: Account;
  amountTo: Decimal & (string | null);
  baseAmount: Decimal;
  baseAmountTo: Decimal & (string | null);
  taskRecordId: string | null;
  createdAt: string;
  updatedAt: string;
};

export type TransactionListMeta = {
  total: number;
  page: number;
  limit: number;
  count: number;
};

export type TransactionListResponse = {
  items: Array<TransactionResponse>;
  meta: TransactionListMeta;
};

export type TransactionCreateRequest = {
  transactionDate: string;
  categoryId?: string | null;
  type: TransactionType;
  description?: string | null;
  accountId: string;
  amount: Decimal;
  accountToId?: string | null;
  amountTo?: Decimal & (string | null);
};

export type TransactionUpdateRequest = {
  transactionDate?: string;
  categoryId?: string | null;
  type?: TransactionType;
  description?: string | null;
  accountId?: string;
  amount?: Decimal;
  accountToId?: string | null;
  amountTo?: Decimal & (string | null);
};

export type GetTokenData = {
  /**
   * Token request
   */
  body: TokenRequest;
  path?: never;
  query?: never;
  url: "/api/v1/auth/token";
};

export type GetTokenErrors = {
  /**
   * Bad request
   */
  400: CommonError;
  /**
   * Validation error
   */
  422: ValidationError;
};

export type GetTokenError = GetTokenErrors[keyof GetTokenErrors];

export type GetTokenResponses = {
  /**
   * Access token
   */
  200: TokenResponse;
};

export type GetTokenResponse = GetTokenResponses[keyof GetTokenResponses];

export type LogoutData = {
  body?: never;
  path?: never;
  query?: never;
  url: "/api/v1/auth/logout";
};

export type LogoutResponses = {
  /**
   * Successfully logged out
   */
  204: void;
};

export type LogoutResponse = LogoutResponses[keyof LogoutResponses];

export type CreateUserData = {
  /**
   * User to create
   */
  body: UserCreateRequest;
  path?: never;
  query?: never;
  url: "/api/v1/users";
};

export type CreateUserErrors = {
  /**
   * Bad request
   */
  400: CommonError;
  /**
   * User already exists
   */
  409: CommonError;
  /**
   * Validation error
   */
  422: ValidationError;
};

export type CreateUserError = CreateUserErrors[keyof CreateUserErrors];

export type CreateUserResponses = {
  /**
   * User created
   */
  201: User;
};

export type CreateUserResponse = CreateUserResponses[keyof CreateUserResponses];

export type GetCurrentUserData = {
  body?: never;
  path?: never;
  query?: never;
  url: "/api/v1/users/me";
};

export type GetCurrentUserErrors = {
  /**
   * Bad request
   */
  400: CommonError;
  /**
   * Unauthorized
   */
  401: CommonError;
};

export type GetCurrentUserError = GetCurrentUserErrors[keyof GetCurrentUserErrors];

export type GetCurrentUserResponses = {
  /**
   * Current user
   */
  200: User;
};

export type GetCurrentUserResponse = GetCurrentUserResponses[keyof GetCurrentUserResponses];

export type ListAccountGroupsData = {
  body?: never;
  path?: never;
  query?: never;
  url: "/api/v1/account-groups";
};

export type ListAccountGroupsErrors = {
  /**
   * Unauthorized
   */
  401: CommonError;
};

export type ListAccountGroupsError = ListAccountGroupsErrors[keyof ListAccountGroupsErrors];

export type ListAccountGroupsResponses = {
  /**
   * Account groups retrieved
   */
  200: Array<AccountGroup>;
};

export type ListAccountGroupsResponse = ListAccountGroupsResponses[keyof ListAccountGroupsResponses];

export type CreateAccountGroupData = {
  /**
   * Account group to create
   */
  body: AccountGroupCreateRequest;
  path?: never;
  query?: never;
  url: "/api/v1/account-groups";
};

export type CreateAccountGroupErrors = {
  /**
   * Bad request
   */
  400: CommonError;
  /**
   * Unauthorized
   */
  401: CommonError;
  /**
   * Validation error
   */
  422: ValidationError;
};

export type CreateAccountGroupError = CreateAccountGroupErrors[keyof CreateAccountGroupErrors];

export type CreateAccountGroupResponses = {
  /**
   * Account group created
   */
  201: AccountGroup;
};

export type CreateAccountGroupResponse = CreateAccountGroupResponses[keyof CreateAccountGroupResponses];

export type DeleteAccountGroupData = {
  body?: never;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/v1/account-groups/{id}";
};

export type DeleteAccountGroupErrors = {
  /**
   * Unauthorized
   */
  401: CommonError;
  /**
   * Account group not found
   */
  404: CommonError;
};

export type DeleteAccountGroupError = DeleteAccountGroupErrors[keyof DeleteAccountGroupErrors];

export type DeleteAccountGroupResponses = {
  /**
   * Account group deleted
   */
  204: void;
};

export type DeleteAccountGroupResponse = DeleteAccountGroupResponses[keyof DeleteAccountGroupResponses];

export type GetAccountGroupData = {
  body?: never;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/v1/account-groups/{id}";
};

export type GetAccountGroupErrors = {
  /**
   * Unauthorized
   */
  401: CommonError;
  /**
   * Account group not found
   */
  404: CommonError;
};

export type GetAccountGroupError = GetAccountGroupErrors[keyof GetAccountGroupErrors];

export type GetAccountGroupResponses = {
  /**
   * Account group retrieved
   */
  200: AccountGroup;
};

export type GetAccountGroupResponse = GetAccountGroupResponses[keyof GetAccountGroupResponses];

export type UpdateAccountGroupData = {
  /**
   * Account group data to update
   */
  body: AccountGroupUpdateRequest;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/v1/account-groups/{id}";
};

export type UpdateAccountGroupErrors = {
  /**
   * Unauthorized
   */
  401: CommonError;
  /**
   * Account group not found
   */
  404: CommonError;
  /**
   * Validation error
   */
  422: ValidationError;
};

export type UpdateAccountGroupError = UpdateAccountGroupErrors[keyof UpdateAccountGroupErrors];

export type UpdateAccountGroupResponses = {
  /**
   * Account group updated
   */
  200: AccountGroup;
};

export type UpdateAccountGroupResponse = UpdateAccountGroupResponses[keyof UpdateAccountGroupResponses];

export type ListAccountsData = {
  body?: never;
  path?: never;
  query?: never;
  url: "/api/v1/accounts";
};

export type ListAccountsErrors = {
  /**
   * Unauthorized
   */
  401: CommonError;
};

export type ListAccountsError = ListAccountsErrors[keyof ListAccountsErrors];

export type ListAccountsResponses = {
  /**
   * Accounts retrieved
   */
  200: Array<Account>;
};

export type ListAccountsResponse = ListAccountsResponses[keyof ListAccountsResponses];

export type CreateAccountData = {
  /**
   * Account to create
   */
  body: AccountCreateRequest;
  path?: never;
  query?: never;
  url: "/api/v1/accounts";
};

export type CreateAccountErrors = {
  /**
   * Bad request
   */
  400: CommonError;
  /**
   * Unauthorized
   */
  401: CommonError;
  /**
   * Validation error
   */
  422: ValidationError;
};

export type CreateAccountError = CreateAccountErrors[keyof CreateAccountErrors];

export type CreateAccountResponses = {
  /**
   * Account created
   */
  201: Account;
};

export type CreateAccountResponse = CreateAccountResponses[keyof CreateAccountResponses];

export type DeleteAccountData = {
  body?: never;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/v1/accounts/{id}";
};

export type DeleteAccountErrors = {
  /**
   * Unauthorized
   */
  401: CommonError;
  /**
   * Account not found
   */
  404: CommonError;
};

export type DeleteAccountError = DeleteAccountErrors[keyof DeleteAccountErrors];

export type DeleteAccountResponses = {
  /**
   * Account deleted
   */
  204: void;
};

export type DeleteAccountResponse = DeleteAccountResponses[keyof DeleteAccountResponses];

export type GetAccountData = {
  body?: never;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/v1/accounts/{id}";
};

export type GetAccountErrors = {
  /**
   * Unauthorized
   */
  401: CommonError;
  /**
   * Account not found
   */
  404: CommonError;
};

export type GetAccountError = GetAccountErrors[keyof GetAccountErrors];

export type GetAccountResponses = {
  /**
   * Account retrieved
   */
  200: Account;
};

export type GetAccountResponse = GetAccountResponses[keyof GetAccountResponses];

export type UpdateAccountData = {
  /**
   * Account data to update
   */
  body: AccountUpdateRequest;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/v1/accounts/{id}";
};

export type UpdateAccountErrors = {
  /**
   * Unauthorized
   */
  401: CommonError;
  /**
   * Account not found
   */
  404: CommonError;
  /**
   * Validation error
   */
  422: ValidationError;
};

export type UpdateAccountError = UpdateAccountErrors[keyof UpdateAccountErrors];

export type UpdateAccountResponses = {
  /**
   * Account updated
   */
  200: Account;
};

export type UpdateAccountResponse = UpdateAccountResponses[keyof UpdateAccountResponses];

export type UpdateAccountBalanceData = {
  /**
   * New balance data
   */
  body: AccountBalanceUpdateRequest;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/v1/accounts/{id}/balance";
};

export type UpdateAccountBalanceErrors = {
  /**
   * Unauthorized
   */
  401: CommonError;
  /**
   * Account not found
   */
  404: CommonError;
  /**
   * Validation error
   */
  422: ValidationError;
};

export type UpdateAccountBalanceError = UpdateAccountBalanceErrors[keyof UpdateAccountBalanceErrors];

export type UpdateAccountBalanceResponses = {
  /**
   * Account balance updated
   */
  200: Account;
};

export type UpdateAccountBalanceResponse = UpdateAccountBalanceResponses[keyof UpdateAccountBalanceResponses];

export type ListBudgetsData = {
  body?: never;
  path?: never;
  query?: {
    includeArchived?: boolean | null;
  };
  url: "/api/v1/budgets";
};

export type ListBudgetsErrors = {
  /**
   * Unauthorized
   */
  401: CommonError;
  /**
   * Validation error
   */
  422: ValidationError;
};

export type ListBudgetsError = ListBudgetsErrors[keyof ListBudgetsErrors];

export type ListBudgetsResponses = {
  /**
   * Budgets retrieved
   */
  200: BudgetListResponse;
};

export type ListBudgetsResponse = ListBudgetsResponses[keyof ListBudgetsResponses];

export type CreateBudgetData = {
  /**
   * Budget data
   */
  body: BudgetCreateRequest;
  path?: never;
  query?: never;
  url: "/api/v1/budgets";
};

export type CreateBudgetErrors = {
  /**
   * Bad request
   */
  400: CommonError;
  /**
   * Unauthorized
   */
  401: CommonError;
  /**
   * Validation error
   */
  422: ValidationError;
};

export type CreateBudgetError = CreateBudgetErrors[keyof CreateBudgetErrors];

export type CreateBudgetResponses = {
  /**
   * Budget created
   */
  201: Budget;
};

export type CreateBudgetResponse = CreateBudgetResponses[keyof CreateBudgetResponses];

export type DeleteBudgetData = {
  body?: never;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/v1/budgets/{id}";
};

export type DeleteBudgetErrors = {
  /**
   * Unauthorized
   */
  401: CommonError;
  /**
   * Budget not found
   */
  404: CommonError;
  /**
   * Validation error
   */
  422: ValidationError;
};

export type DeleteBudgetError = DeleteBudgetErrors[keyof DeleteBudgetErrors];

export type DeleteBudgetResponses = {
  /**
   * Budget deleted
   */
  204: void;
};

export type DeleteBudgetResponse = DeleteBudgetResponses[keyof DeleteBudgetResponses];

export type GetBudgetData = {
  body?: never;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/v1/budgets/{id}";
};

export type GetBudgetErrors = {
  /**
   * Unauthorized
   */
  401: CommonError;
  /**
   * Budget not found
   */
  404: CommonError;
  /**
   * Validation error
   */
  422: ValidationError;
};

export type GetBudgetError = GetBudgetErrors[keyof GetBudgetErrors];

export type GetBudgetResponses = {
  /**
   * Budget retrieved
   */
  200: Budget;
};

export type GetBudgetResponse = GetBudgetResponses[keyof GetBudgetResponses];

export type UpdateBudgetData = {
  /**
   * Budget update data
   */
  body: BudgetUpdateRequest;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/v1/budgets/{id}";
};

export type UpdateBudgetErrors = {
  /**
   * Bad request
   */
  400: CommonError;
  /**
   * Unauthorized
   */
  401: CommonError;
  /**
   * Budget not found
   */
  404: CommonError;
  /**
   * Validation error
   */
  422: ValidationError;
};

export type UpdateBudgetError = UpdateBudgetErrors[keyof UpdateBudgetErrors];

export type UpdateBudgetResponses = {
  /**
   * Budget updated
   */
  200: Budget;
};

export type UpdateBudgetResponse = UpdateBudgetResponses[keyof UpdateBudgetResponses];

export type GetBudgetHistoryData = {
  body?: never;
  path: {
    id: string;
  };
  query?: {
    /**
     * Page number
     */
    page?: number;
    /**
     * Items per page
     */
    limit?: number;
  };
  url: "/api/v1/budgets/{id}/history";
};

export type GetBudgetHistoryErrors = {
  /**
   * Unauthorized
   */
  401: CommonError;
  /**
   * Budget not found
   */
  404: CommonError;
  /**
   * Validation error
   */
  422: ValidationError;
};

export type GetBudgetHistoryError = GetBudgetHistoryErrors[keyof GetBudgetHistoryErrors];

export type GetBudgetHistoryResponses = {
  /**
   * Budget history retrieved
   */
  200: BudgetHistoryResponse;
};

export type GetBudgetHistoryResponse = GetBudgetHistoryResponses[keyof GetBudgetHistoryResponses];

export type ListCategoriesData = {
  body?: never;
  path?: never;
  query?: never;
  url: "/api/v1/categories";
};

export type ListCategoriesErrors = {
  /**
   * Unauthorized
   */
  401: CommonError;
};

export type ListCategoriesError = ListCategoriesErrors[keyof ListCategoriesErrors];

export type ListCategoriesResponses = {
  /**
   * Categories retrieved
   */
  200: Array<Category>;
};

export type ListCategoriesResponse = ListCategoriesResponses[keyof ListCategoriesResponses];

export type CreateCategoryData = {
  /**
   * Category to create
   */
  body: CategoryCreateRequest;
  path?: never;
  query?: never;
  url: "/api/v1/categories";
};

export type CreateCategoryErrors = {
  /**
   * Bad request
   */
  400: CommonError;
  /**
   * Unauthorized
   */
  401: CommonError;
  /**
   * Validation error
   */
  422: ValidationError;
};

export type CreateCategoryError = CreateCategoryErrors[keyof CreateCategoryErrors];

export type CreateCategoryResponses = {
  /**
   * Category created
   */
  201: Category;
};

export type CreateCategoryResponse = CreateCategoryResponses[keyof CreateCategoryResponses];

export type DeleteCategoryData = {
  body?: never;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/v1/categories/{id}";
};

export type DeleteCategoryErrors = {
  /**
   * Unauthorized
   */
  401: CommonError;
  /**
   * Category not found
   */
  404: CommonError;
};

export type DeleteCategoryError = DeleteCategoryErrors[keyof DeleteCategoryErrors];

export type DeleteCategoryResponses = {
  /**
   * Category deleted
   */
  204: void;
};

export type DeleteCategoryResponse = DeleteCategoryResponses[keyof DeleteCategoryResponses];

export type GetCategoryData = {
  body?: never;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/v1/categories/{id}";
};

export type GetCategoryErrors = {
  /**
   * Unauthorized
   */
  401: CommonError;
  /**
   * Category not found
   */
  404: CommonError;
};

export type GetCategoryError = GetCategoryErrors[keyof GetCategoryErrors];

export type GetCategoryResponses = {
  /**
   * Category retrieved
   */
  200: Category;
};

export type GetCategoryResponse = GetCategoryResponses[keyof GetCategoryResponses];

export type UpdateCategoryData = {
  /**
   * Category data to update
   */
  body: CategoryUpdateRequest;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/v1/categories/{id}";
};

export type UpdateCategoryErrors = {
  /**
   * Unauthorized
   */
  401: CommonError;
  /**
   * Category not found
   */
  404: CommonError;
  /**
   * Validation error
   */
  422: ValidationError;
};

export type UpdateCategoryError = UpdateCategoryErrors[keyof UpdateCategoryErrors];

export type UpdateCategoryResponses = {
  /**
   * Category updated
   */
  200: Category;
};

export type UpdateCategoryResponse = UpdateCategoryResponses[keyof UpdateCategoryResponses];

export type ListGoalsData = {
  body?: never;
  path?: never;
  query?: never;
  url: "/api/v1/goals";
};

export type ListGoalsErrors = {
  /**
   * Unauthorized
   */
  401: CommonError;
};

export type ListGoalsError = ListGoalsErrors[keyof ListGoalsErrors];

export type ListGoalsResponses = {
  /**
   * Goals retrieved
   */
  200: Array<Goal>;
};

export type ListGoalsResponse = ListGoalsResponses[keyof ListGoalsResponses];

export type CreateGoalData = {
  /**
   * Goal to create
   */
  body: GoalCreateRequest;
  path?: never;
  query?: never;
  url: "/api/v1/goals";
};

export type CreateGoalErrors = {
  /**
   * Bad request
   */
  400: CommonError;
  /**
   * Unauthorized
   */
  401: CommonError;
  /**
   * Validation error
   */
  422: ValidationError;
};

export type CreateGoalError = CreateGoalErrors[keyof CreateGoalErrors];

export type CreateGoalResponses = {
  /**
   * Goal created
   */
  201: Goal;
};

export type CreateGoalResponse = CreateGoalResponses[keyof CreateGoalResponses];

export type DeleteGoalData = {
  body?: never;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/v1/goals/{id}";
};

export type DeleteGoalErrors = {
  /**
   * Unauthorized
   */
  401: CommonError;
  /**
   * Goal not found
   */
  404: CommonError;
};

export type DeleteGoalError = DeleteGoalErrors[keyof DeleteGoalErrors];

export type DeleteGoalResponses = {
  /**
   * Goal deleted
   */
  200: {
    message: string;
  };
};

export type DeleteGoalResponse = DeleteGoalResponses[keyof DeleteGoalResponses];

export type GetGoalData = {
  body?: never;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/v1/goals/{id}";
};

export type GetGoalErrors = {
  /**
   * Unauthorized
   */
  401: CommonError;
  /**
   * Goal not found
   */
  404: CommonError;
};

export type GetGoalError = GetGoalErrors[keyof GetGoalErrors];

export type GetGoalResponses = {
  /**
   * Goal retrieved
   */
  200: Goal;
};

export type GetGoalResponse = GetGoalResponses[keyof GetGoalResponses];

export type UpdateGoalData = {
  /**
   * Goal data to update
   */
  body: GoalUpdateRequest;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/v1/goals/{id}";
};

export type UpdateGoalErrors = {
  /**
   * Bad request
   */
  400: CommonError;
  /**
   * Unauthorized
   */
  401: CommonError;
  /**
   * Goal not found
   */
  404: CommonError;
  /**
   * Validation error
   */
  422: ValidationError;
};

export type UpdateGoalError = UpdateGoalErrors[keyof UpdateGoalErrors];

export type UpdateGoalResponses = {
  /**
   * Goal updated
   */
  200: Goal;
};

export type UpdateGoalResponse = UpdateGoalResponses[keyof UpdateGoalResponses];

export type ListTasksData = {
  body?: never;
  path?: never;
  query?: {
    /**
     * Page number
     */
    page?: number;
    /**
     * Items per page
     */
    limit?: number;
  };
  url: "/api/v1/tasks";
};

export type ListTasksErrors = {
  /**
   * Unauthorized
   */
  401: CommonError;
};

export type ListTasksError = ListTasksErrors[keyof ListTasksErrors];

export type ListTasksResponses = {
  /**
   * Tasks retrieved
   */
  200: TaskListResponse;
};

export type ListTasksResponse = ListTasksResponses[keyof ListTasksResponses];

export type CreateTaskData = {
  /**
   * Task to create
   */
  body: TaskCreateRequest;
  path?: never;
  query?: never;
  url: "/api/v1/tasks";
};

export type CreateTaskErrors = {
  /**
   * Bad request
   */
  400: CommonError;
  /**
   * Unauthorized
   */
  401: CommonError;
  /**
   * Validation error
   */
  422: ValidationError;
};

export type CreateTaskError = CreateTaskErrors[keyof CreateTaskErrors];

export type CreateTaskResponses = {
  /**
   * Task created
   */
  201: TaskResponse;
};

export type CreateTaskResponse = CreateTaskResponses[keyof CreateTaskResponses];

export type DeleteTaskData = {
  body?: never;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/v1/tasks/{id}";
};

export type DeleteTaskErrors = {
  /**
   * Unauthorized
   */
  401: CommonError;
  /**
   * Task not found
   */
  404: CommonError;
};

export type DeleteTaskError = DeleteTaskErrors[keyof DeleteTaskErrors];

export type DeleteTaskResponses = {
  /**
   * Task deleted
   */
  204: void;
};

export type DeleteTaskResponse = DeleteTaskResponses[keyof DeleteTaskResponses];

export type GetTaskData = {
  body?: never;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/v1/tasks/{id}";
};

export type GetTaskErrors = {
  /**
   * Unauthorized
   */
  401: CommonError;
  /**
   * Task not found
   */
  404: CommonError;
};

export type GetTaskError = GetTaskErrors[keyof GetTaskErrors];

export type GetTaskResponses = {
  /**
   * Task retrieved
   */
  200: TaskResponse;
};

export type GetTaskResponse = GetTaskResponses[keyof GetTaskResponses];

export type UpdateTaskData = {
  /**
   * Task data to update
   */
  body: TaskUpdateRequest;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/v1/tasks/{id}";
};

export type UpdateTaskErrors = {
  /**
   * Unauthorized
   */
  401: CommonError;
  /**
   * Task not found
   */
  404: CommonError;
  /**
   * Validation error
   */
  422: ValidationError;
};

export type UpdateTaskError = UpdateTaskErrors[keyof UpdateTaskErrors];

export type UpdateTaskResponses = {
  /**
   * Task updated
   */
  200: TaskResponse;
};

export type UpdateTaskResponse = UpdateTaskResponses[keyof UpdateTaskResponses];

export type UpdateTaskStatusData = {
  /**
   * Task status update data
   */
  body: TaskStatusUpdateRequest;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/v1/tasks/{id}/status";
};

export type UpdateTaskStatusErrors = {
  /**
   * Bad request
   */
  400: CommonError;
  /**
   * Unauthorized
   */
  401: CommonError;
  /**
   * Task not found
   */
  404: CommonError;
  /**
   * Validation error
   */
  422: ValidationError;
};

export type UpdateTaskStatusError = UpdateTaskStatusErrors[keyof UpdateTaskStatusErrors];

export type UpdateTaskStatusResponses = {
  /**
   * Task status updated
   */
  200: unknown;
};

export type GetTaskHistoryData = {
  body?: never;
  path: {
    id: string;
  };
  query?: {
    /**
     * Page number
     */
    page?: number;
    /**
     * Items per page
     */
    limit?: number;
  };
  url: "/api/v1/tasks/{id}/history";
};

export type GetTaskHistoryErrors = {
  /**
   * Unauthorized
   */
  401: CommonError;
  /**
   * Task not found
   */
  404: CommonError;
};

export type GetTaskHistoryError = GetTaskHistoryErrors[keyof GetTaskHistoryErrors];

export type GetTaskHistoryResponses = {
  /**
   * Task history retrieved
   */
  200: TaskHistoryResponse;
};

export type GetTaskHistoryResponse = GetTaskHistoryResponses[keyof GetTaskHistoryResponses];

export type RemoveTaskTransactionsData = {
  /**
   * Transactions to remove
   */
  body: TaskTransactionAssignment;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/v1/tasks/{id}/transactions";
};

export type RemoveTaskTransactionsErrors = {
  /**
   * Bad request
   */
  400: CommonError;
  /**
   * Unauthorized
   */
  401: CommonError;
  /**
   * Task not found
   */
  404: CommonError;
  /**
   * Validation error
   */
  422: ValidationError;
};

export type RemoveTaskTransactionsError = RemoveTaskTransactionsErrors[keyof RemoveTaskTransactionsErrors];

export type RemoveTaskTransactionsResponses = {
  /**
   * Transactions removed
   */
  200: unknown;
};

export type AssignTaskTransactionsData = {
  /**
   * Transactions to assign
   */
  body: TaskTransactionAssignment;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/v1/tasks/{id}/transactions";
};

export type AssignTaskTransactionsErrors = {
  /**
   * Bad request
   */
  400: CommonError;
  /**
   * Unauthorized
   */
  401: CommonError;
  /**
   * Task not found
   */
  404: CommonError;
  /**
   * Validation error
   */
  422: ValidationError;
};

export type AssignTaskTransactionsError = AssignTaskTransactionsErrors[keyof AssignTaskTransactionsErrors];

export type AssignTaskTransactionsResponses = {
  /**
   * Transactions assigned
   */
  200: unknown;
};

export type ReplaceTaskTransactionsData = {
  /**
   * Transactions to replace with
   */
  body: TaskTransactionAssignment;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/v1/tasks/{id}/transactions";
};

export type ReplaceTaskTransactionsErrors = {
  /**
   * Bad request
   */
  400: CommonError;
  /**
   * Unauthorized
   */
  401: CommonError;
  /**
   * Task not found
   */
  404: CommonError;
  /**
   * Validation error
   */
  422: ValidationError;
};

export type ReplaceTaskTransactionsError = ReplaceTaskTransactionsErrors[keyof ReplaceTaskTransactionsErrors];

export type ReplaceTaskTransactionsResponses = {
  /**
   * Transactions replaced
   */
  200: unknown;
};

export type ListTransactionsData = {
  body?: never;
  path?: never;
  query?: {
    page?: number;
    limit?: number;
  };
  url: "/api/v1/transactions";
};

export type ListTransactionsErrors = {
  /**
   * Unauthorized
   */
  401: CommonError;
};

export type ListTransactionsError = ListTransactionsErrors[keyof ListTransactionsErrors];

export type ListTransactionsResponses = {
  /**
   * Paginated list of transactions
   */
  200: TransactionListResponse;
};

export type ListTransactionsResponse = ListTransactionsResponses[keyof ListTransactionsResponses];

export type CreateTransactionData = {
  /**
   * Transaction data
   */
  body: TransactionCreateRequest;
  path?: never;
  query?: never;
  url: "/api/v1/transactions";
};

export type CreateTransactionErrors = {
  /**
   * Validation error
   */
  400: ValidationError;
  /**
   * Unauthorized
   */
  401: CommonError;
};

export type CreateTransactionError = CreateTransactionErrors[keyof CreateTransactionErrors];

export type CreateTransactionResponses = {
  /**
   * Transaction created successfully
   */
  201: TransactionResponse;
};

export type CreateTransactionResponse = CreateTransactionResponses[keyof CreateTransactionResponses];

export type DeleteTransactionData = {
  body?: never;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/v1/transactions/{id}";
};

export type DeleteTransactionErrors = {
  /**
   * Unauthorized
   */
  401: CommonError;
  /**
   * Transaction not found
   */
  404: CommonError;
};

export type DeleteTransactionError = DeleteTransactionErrors[keyof DeleteTransactionErrors];

export type DeleteTransactionResponses = {
  /**
   * Transaction deleted successfully
   */
  204: void;
};

export type DeleteTransactionResponse = DeleteTransactionResponses[keyof DeleteTransactionResponses];

export type GetTransactionData = {
  body?: never;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/v1/transactions/{id}";
};

export type GetTransactionErrors = {
  /**
   * Unauthorized
   */
  401: CommonError;
  /**
   * Transaction not found
   */
  404: CommonError;
};

export type GetTransactionError = GetTransactionErrors[keyof GetTransactionErrors];

export type GetTransactionResponses = {
  /**
   * Transaction details
   */
  200: TransactionResponse;
};

export type GetTransactionResponse = GetTransactionResponses[keyof GetTransactionResponses];

export type UpdateTransactionData = {
  /**
   * Updated transaction data
   */
  body: TransactionUpdateRequest;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/v1/transactions/{id}";
};

export type UpdateTransactionErrors = {
  /**
   * Validation error
   */
  400: ValidationError;
  /**
   * Unauthorized
   */
  401: CommonError;
  /**
   * Transaction not found
   */
  404: CommonError;
};

export type UpdateTransactionError = UpdateTransactionErrors[keyof UpdateTransactionErrors];

export type UpdateTransactionResponses = {
  /**
   * Transaction updated successfully
   */
  200: TransactionResponse;
};

export type UpdateTransactionResponse = UpdateTransactionResponses[keyof UpdateTransactionResponses];

export type ClientOptions = {
  baseUrl: "http://127.0.0.1:5000" | (string & {});
};
