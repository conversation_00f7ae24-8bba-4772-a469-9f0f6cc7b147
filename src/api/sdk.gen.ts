// This file is auto-generated by @hey-api/openapi-ts

import type { Client, Options as ClientOptions, TDataShape } from "./client";
import type {
  AssignTaskTransactionsData,
  AssignTaskTransactionsErrors,
  AssignTaskTransactionsResponses,
  CreateAccountData,
  CreateAccountErrors,
  CreateAccountGroupData,
  CreateAccountGroupErrors,
  CreateAccountGroupResponses,
  CreateAccountResponses,
  CreateBudgetData,
  CreateBudgetErrors,
  CreateBudgetResponses,
  CreateCategoryData,
  CreateCategoryErrors,
  CreateCategoryResponses,
  CreateGoalData,
  CreateGoalErrors,
  CreateGoalResponses,
  CreateTaskData,
  CreateTaskErrors,
  CreateTaskResponses,
  CreateTransactionData,
  CreateTransactionErrors,
  CreateTransactionResponses,
  CreateUserData,
  CreateUserErrors,
  CreateUserResponses,
  DeleteAccountData,
  DeleteAccountErrors,
  DeleteAccountGroupData,
  DeleteAccountGroupErrors,
  DeleteAccountGroupResponses,
  DeleteAccountResponses,
  DeleteBudgetData,
  DeleteBudgetErrors,
  DeleteBudgetResponses,
  DeleteCategoryData,
  DeleteCategoryErrors,
  DeleteCategoryResponses,
  DeleteGoalData,
  DeleteGoalErrors,
  DeleteGoalResponses,
  DeleteTaskData,
  DeleteTaskErrors,
  DeleteTaskResponses,
  DeleteTransactionData,
  DeleteTransactionErrors,
  DeleteTransactionResponses,
  GetAccountData,
  GetAccountErrors,
  GetAccountGroupData,
  GetAccountGroupErrors,
  GetAccountGroupResponses,
  GetAccountResponses,
  GetBudgetData,
  GetBudgetErrors,
  GetBudgetHistoryData,
  GetBudgetHistoryErrors,
  GetBudgetHistoryResponses,
  GetBudgetResponses,
  GetCategoryData,
  GetCategoryErrors,
  GetCategoryResponses,
  GetCurrentUserData,
  GetCurrentUserErrors,
  GetCurrentUserResponses,
  GetGoalData,
  GetGoalErrors,
  GetGoalResponses,
  GetTaskData,
  GetTaskErrors,
  GetTaskHistoryData,
  GetTaskHistoryErrors,
  GetTaskHistoryResponses,
  GetTaskResponses,
  GetTokenData,
  GetTokenErrors,
  GetTokenResponses,
  GetTransactionData,
  GetTransactionErrors,
  GetTransactionResponses,
  ListAccountGroupsData,
  ListAccountGroupsErrors,
  ListAccountGroupsResponses,
  ListAccountsData,
  ListAccountsErrors,
  ListAccountsResponses,
  ListBudgetsData,
  ListBudgetsErrors,
  ListBudgetsResponses,
  ListCategoriesData,
  ListCategoriesErrors,
  ListCategoriesResponses,
  ListGoalsData,
  ListGoalsErrors,
  ListGoalsResponses,
  ListTasksData,
  ListTasksErrors,
  ListTasksResponses,
  ListTransactionsData,
  ListTransactionsErrors,
  ListTransactionsResponses,
  LogoutData,
  LogoutResponses,
  RemoveTaskTransactionsData,
  RemoveTaskTransactionsErrors,
  RemoveTaskTransactionsResponses,
  ReplaceTaskTransactionsData,
  ReplaceTaskTransactionsErrors,
  ReplaceTaskTransactionsResponses,
  UpdateAccountBalanceData,
  UpdateAccountBalanceErrors,
  UpdateAccountBalanceResponses,
  UpdateAccountData,
  UpdateAccountErrors,
  UpdateAccountGroupData,
  UpdateAccountGroupErrors,
  UpdateAccountGroupResponses,
  UpdateAccountResponses,
  UpdateBudgetData,
  UpdateBudgetErrors,
  UpdateBudgetResponses,
  UpdateCategoryData,
  UpdateCategoryErrors,
  UpdateCategoryResponses,
  UpdateGoalData,
  UpdateGoalErrors,
  UpdateGoalResponses,
  UpdateTaskData,
  UpdateTaskErrors,
  UpdateTaskResponses,
  UpdateTaskStatusData,
  UpdateTaskStatusErrors,
  UpdateTaskStatusResponses,
  UpdateTransactionData,
  UpdateTransactionErrors,
  UpdateTransactionResponses,
} from "./types.gen";

import { client as _heyApiClient } from "./client.gen";

export type Options<TData extends TDataShape = TDataShape, ThrowOnError extends boolean = boolean> = ClientOptions<
  TData,
  ThrowOnError
> & {
  /**
   * You can provide a client instance returned by `createClient()` instead of
   * individual options. This might be also useful if you want to implement a
   * custom client.
   */
  client?: Client;
  /**
   * You can pass arbitrary values through the `meta` object. This can be
   * used to access values that aren't defined as part of the SDK function.
   */
  meta?: Record<string, unknown>;
};

/**
 * Get access token
 * Get access token for the user. Generated token should be used in the `Authorization` header or `access_token` cookie.
 */
export const getToken = <ThrowOnError extends boolean = false>(options: Options<GetTokenData, ThrowOnError>) => {
  return (options.client ?? _heyApiClient).post<GetTokenResponses, GetTokenErrors, ThrowOnError>({
    url: "/api/v1/auth/token",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options.headers,
    },
  });
};

/**
 * Logout user
 * Logout user by clearing the access token cookie.
 */
export const logout = <ThrowOnError extends boolean = false>(options?: Options<LogoutData, ThrowOnError>) => {
  return (options?.client ?? _heyApiClient).delete<LogoutResponses, unknown, ThrowOnError>({
    url: "/api/v1/auth/logout",
    ...options,
  });
};

/**
 * Create user
 * Create (register) a new user. It returns the created user, but access tokens will not be generated and should be requested separately.
 */
export const createUser = <ThrowOnError extends boolean = false>(options: Options<CreateUserData, ThrowOnError>) => {
  return (options.client ?? _heyApiClient).post<CreateUserResponses, CreateUserErrors, ThrowOnError>({
    url: "/api/v1/users",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options.headers,
    },
  });
};

/**
 * Current user
 * Get current user details
 */
export const getCurrentUser = <ThrowOnError extends boolean = false>(
  options?: Options<GetCurrentUserData, ThrowOnError>
) => {
  return (options?.client ?? _heyApiClient).get<GetCurrentUserResponses, GetCurrentUserErrors, ThrowOnError>({
    security: [
      {
        scheme: "bearer",
        type: "http",
      },
    ],
    url: "/api/v1/users/me",
    ...options,
  });
};

/**
 * List account groups
 * Get all account groups for the current user
 */
export const listAccountGroups = <ThrowOnError extends boolean = false>(
  options?: Options<ListAccountGroupsData, ThrowOnError>
) => {
  return (options?.client ?? _heyApiClient).get<ListAccountGroupsResponses, ListAccountGroupsErrors, ThrowOnError>({
    security: [
      {
        scheme: "bearer",
        type: "http",
      },
    ],
    url: "/api/v1/account-groups",
    ...options,
  });
};

/**
 * Create account group
 * Create a new account group
 */
export const createAccountGroup = <ThrowOnError extends boolean = false>(
  options: Options<CreateAccountGroupData, ThrowOnError>
) => {
  return (options.client ?? _heyApiClient).post<CreateAccountGroupResponses, CreateAccountGroupErrors, ThrowOnError>({
    security: [
      {
        scheme: "bearer",
        type: "http",
      },
    ],
    url: "/api/v1/account-groups",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options.headers,
    },
  });
};

/**
 * Delete account group
 * Delete account group by ID
 */
export const deleteAccountGroup = <ThrowOnError extends boolean = false>(
  options: Options<DeleteAccountGroupData, ThrowOnError>
) => {
  return (options.client ?? _heyApiClient).delete<DeleteAccountGroupResponses, DeleteAccountGroupErrors, ThrowOnError>({
    security: [
      {
        scheme: "bearer",
        type: "http",
      },
    ],
    url: "/api/v1/account-groups/{id}",
    ...options,
  });
};

/**
 * Get account group
 * Get account group by ID
 */
export const getAccountGroup = <ThrowOnError extends boolean = false>(
  options: Options<GetAccountGroupData, ThrowOnError>
) => {
  return (options.client ?? _heyApiClient).get<GetAccountGroupResponses, GetAccountGroupErrors, ThrowOnError>({
    security: [
      {
        scheme: "bearer",
        type: "http",
      },
    ],
    url: "/api/v1/account-groups/{id}",
    ...options,
  });
};

/**
 * Update account group
 * Update account group by ID
 */
export const updateAccountGroup = <ThrowOnError extends boolean = false>(
  options: Options<UpdateAccountGroupData, ThrowOnError>
) => {
  return (options.client ?? _heyApiClient).put<UpdateAccountGroupResponses, UpdateAccountGroupErrors, ThrowOnError>({
    security: [
      {
        scheme: "bearer",
        type: "http",
      },
    ],
    url: "/api/v1/account-groups/{id}",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options.headers,
    },
  });
};

/**
 * List accounts
 * Get all accounts for the current user
 */
export const listAccounts = <ThrowOnError extends boolean = false>(
  options?: Options<ListAccountsData, ThrowOnError>
) => {
  return (options?.client ?? _heyApiClient).get<ListAccountsResponses, ListAccountsErrors, ThrowOnError>({
    security: [
      {
        scheme: "bearer",
        type: "http",
      },
    ],
    url: "/api/v1/accounts",
    ...options,
  });
};

/**
 * Create account
 * Create a new account
 */
export const createAccount = <ThrowOnError extends boolean = false>(
  options: Options<CreateAccountData, ThrowOnError>
) => {
  return (options.client ?? _heyApiClient).post<CreateAccountResponses, CreateAccountErrors, ThrowOnError>({
    security: [
      {
        scheme: "bearer",
        type: "http",
      },
    ],
    url: "/api/v1/accounts",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options.headers,
    },
  });
};

/**
 * Delete account
 * Delete account by ID
 */
export const deleteAccount = <ThrowOnError extends boolean = false>(
  options: Options<DeleteAccountData, ThrowOnError>
) => {
  return (options.client ?? _heyApiClient).delete<DeleteAccountResponses, DeleteAccountErrors, ThrowOnError>({
    security: [
      {
        scheme: "bearer",
        type: "http",
      },
    ],
    url: "/api/v1/accounts/{id}",
    ...options,
  });
};

/**
 * Get account
 * Get account by ID
 */
export const getAccount = <ThrowOnError extends boolean = false>(options: Options<GetAccountData, ThrowOnError>) => {
  return (options.client ?? _heyApiClient).get<GetAccountResponses, GetAccountErrors, ThrowOnError>({
    security: [
      {
        scheme: "bearer",
        type: "http",
      },
    ],
    url: "/api/v1/accounts/{id}",
    ...options,
  });
};

/**
 * Update account
 * Update account by ID
 */
export const updateAccount = <ThrowOnError extends boolean = false>(
  options: Options<UpdateAccountData, ThrowOnError>
) => {
  return (options.client ?? _heyApiClient).put<UpdateAccountResponses, UpdateAccountErrors, ThrowOnError>({
    security: [
      {
        scheme: "bearer",
        type: "http",
      },
    ],
    url: "/api/v1/accounts/{id}",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options.headers,
    },
  });
};

/**
 * Update account balance
 * Update current balance for an account
 */
export const updateAccountBalance = <ThrowOnError extends boolean = false>(
  options: Options<UpdateAccountBalanceData, ThrowOnError>
) => {
  return (options.client ?? _heyApiClient).patch<
    UpdateAccountBalanceResponses,
    UpdateAccountBalanceErrors,
    ThrowOnError
  >({
    security: [
      {
        scheme: "bearer",
        type: "http",
      },
    ],
    url: "/api/v1/accounts/{id}/balance",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options.headers,
    },
  });
};

/**
 * List budgets
 * Get all budgets for the current user
 */
export const listBudgets = <ThrowOnError extends boolean = false>(options?: Options<ListBudgetsData, ThrowOnError>) => {
  return (options?.client ?? _heyApiClient).get<ListBudgetsResponses, ListBudgetsErrors, ThrowOnError>({
    security: [
      {
        scheme: "bearer",
        type: "http",
      },
    ],
    url: "/api/v1/budgets",
    ...options,
  });
};

/**
 * Create budget
 * Create a new budget
 */
export const createBudget = <ThrowOnError extends boolean = false>(
  options: Options<CreateBudgetData, ThrowOnError>
) => {
  return (options.client ?? _heyApiClient).post<CreateBudgetResponses, CreateBudgetErrors, ThrowOnError>({
    security: [
      {
        scheme: "bearer",
        type: "http",
      },
    ],
    url: "/api/v1/budgets",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options.headers,
    },
  });
};

/**
 * Delete budget
 * Delete budget by ID
 */
export const deleteBudget = <ThrowOnError extends boolean = false>(
  options: Options<DeleteBudgetData, ThrowOnError>
) => {
  return (options.client ?? _heyApiClient).delete<DeleteBudgetResponses, DeleteBudgetErrors, ThrowOnError>({
    security: [
      {
        scheme: "bearer",
        type: "http",
      },
    ],
    url: "/api/v1/budgets/{id}",
    ...options,
  });
};

/**
 * Get budget
 * Get budget by ID
 */
export const getBudget = <ThrowOnError extends boolean = false>(options: Options<GetBudgetData, ThrowOnError>) => {
  return (options.client ?? _heyApiClient).get<GetBudgetResponses, GetBudgetErrors, ThrowOnError>({
    security: [
      {
        scheme: "bearer",
        type: "http",
      },
    ],
    url: "/api/v1/budgets/{id}",
    ...options,
  });
};

/**
 * Update budget
 * Update budget by ID
 */
export const updateBudget = <ThrowOnError extends boolean = false>(
  options: Options<UpdateBudgetData, ThrowOnError>
) => {
  return (options.client ?? _heyApiClient).put<UpdateBudgetResponses, UpdateBudgetErrors, ThrowOnError>({
    security: [
      {
        scheme: "bearer",
        type: "http",
      },
    ],
    url: "/api/v1/budgets/{id}",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options.headers,
    },
  });
};

/**
 * Get budget history
 * Get paginated budget records for a specific budget
 */
export const getBudgetHistory = <ThrowOnError extends boolean = false>(
  options: Options<GetBudgetHistoryData, ThrowOnError>
) => {
  return (options.client ?? _heyApiClient).get<GetBudgetHistoryResponses, GetBudgetHistoryErrors, ThrowOnError>({
    security: [
      {
        scheme: "bearer",
        type: "http",
      },
    ],
    url: "/api/v1/budgets/{id}/history",
    ...options,
  });
};

/**
 * List categories
 * Get all categories for the current user
 */
export const listCategories = <ThrowOnError extends boolean = false>(
  options?: Options<ListCategoriesData, ThrowOnError>
) => {
  return (options?.client ?? _heyApiClient).get<ListCategoriesResponses, ListCategoriesErrors, ThrowOnError>({
    security: [
      {
        scheme: "bearer",
        type: "http",
      },
    ],
    url: "/api/v1/categories",
    ...options,
  });
};

/**
 * Create category
 * Create a new category
 */
export const createCategory = <ThrowOnError extends boolean = false>(
  options: Options<CreateCategoryData, ThrowOnError>
) => {
  return (options.client ?? _heyApiClient).post<CreateCategoryResponses, CreateCategoryErrors, ThrowOnError>({
    security: [
      {
        scheme: "bearer",
        type: "http",
      },
    ],
    url: "/api/v1/categories",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options.headers,
    },
  });
};

/**
 * Delete category
 * Delete category by ID
 */
export const deleteCategory = <ThrowOnError extends boolean = false>(
  options: Options<DeleteCategoryData, ThrowOnError>
) => {
  return (options.client ?? _heyApiClient).delete<DeleteCategoryResponses, DeleteCategoryErrors, ThrowOnError>({
    security: [
      {
        scheme: "bearer",
        type: "http",
      },
    ],
    url: "/api/v1/categories/{id}",
    ...options,
  });
};

/**
 * Get category
 * Get category by ID
 */
export const getCategory = <ThrowOnError extends boolean = false>(options: Options<GetCategoryData, ThrowOnError>) => {
  return (options.client ?? _heyApiClient).get<GetCategoryResponses, GetCategoryErrors, ThrowOnError>({
    security: [
      {
        scheme: "bearer",
        type: "http",
      },
    ],
    url: "/api/v1/categories/{id}",
    ...options,
  });
};

/**
 * Update category
 * Update category by ID
 */
export const updateCategory = <ThrowOnError extends boolean = false>(
  options: Options<UpdateCategoryData, ThrowOnError>
) => {
  return (options.client ?? _heyApiClient).put<UpdateCategoryResponses, UpdateCategoryErrors, ThrowOnError>({
    security: [
      {
        scheme: "bearer",
        type: "http",
      },
    ],
    url: "/api/v1/categories/{id}",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options.headers,
    },
  });
};

/**
 * List goals
 * Get all financial goals for the current user
 */
export const listGoals = <ThrowOnError extends boolean = false>(options?: Options<ListGoalsData, ThrowOnError>) => {
  return (options?.client ?? _heyApiClient).get<ListGoalsResponses, ListGoalsErrors, ThrowOnError>({
    security: [
      {
        scheme: "bearer",
        type: "http",
      },
    ],
    url: "/api/v1/goals",
    ...options,
  });
};

/**
 * Create goal
 * Create a new financial goal
 */
export const createGoal = <ThrowOnError extends boolean = false>(options: Options<CreateGoalData, ThrowOnError>) => {
  return (options.client ?? _heyApiClient).post<CreateGoalResponses, CreateGoalErrors, ThrowOnError>({
    security: [
      {
        scheme: "bearer",
        type: "http",
      },
    ],
    url: "/api/v1/goals",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options.headers,
    },
  });
};

/**
 * Delete goal
 * Delete a financial goal
 */
export const deleteGoal = <ThrowOnError extends boolean = false>(options: Options<DeleteGoalData, ThrowOnError>) => {
  return (options.client ?? _heyApiClient).delete<DeleteGoalResponses, DeleteGoalErrors, ThrowOnError>({
    security: [
      {
        scheme: "bearer",
        type: "http",
      },
    ],
    url: "/api/v1/goals/{id}",
    ...options,
  });
};

/**
 * Get goal
 * Get a specific financial goal by ID
 */
export const getGoal = <ThrowOnError extends boolean = false>(options: Options<GetGoalData, ThrowOnError>) => {
  return (options.client ?? _heyApiClient).get<GetGoalResponses, GetGoalErrors, ThrowOnError>({
    security: [
      {
        scheme: "bearer",
        type: "http",
      },
    ],
    url: "/api/v1/goals/{id}",
    ...options,
  });
};

/**
 * Update goal
 * Update a financial goal
 */
export const updateGoal = <ThrowOnError extends boolean = false>(options: Options<UpdateGoalData, ThrowOnError>) => {
  return (options.client ?? _heyApiClient).put<UpdateGoalResponses, UpdateGoalErrors, ThrowOnError>({
    security: [
      {
        scheme: "bearer",
        type: "http",
      },
    ],
    url: "/api/v1/goals/{id}",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options.headers,
    },
  });
};

/**
 * List tasks
 * Get all active tasks for the current user
 */
export const listTasks = <ThrowOnError extends boolean = false>(options?: Options<ListTasksData, ThrowOnError>) => {
  return (options?.client ?? _heyApiClient).get<ListTasksResponses, ListTasksErrors, ThrowOnError>({
    security: [
      {
        scheme: "bearer",
        type: "http",
      },
    ],
    url: "/api/v1/tasks",
    ...options,
  });
};

/**
 * Create task
 * Create a new recurring task
 */
export const createTask = <ThrowOnError extends boolean = false>(options: Options<CreateTaskData, ThrowOnError>) => {
  return (options.client ?? _heyApiClient).post<CreateTaskResponses, CreateTaskErrors, ThrowOnError>({
    security: [
      {
        scheme: "bearer",
        type: "http",
      },
    ],
    url: "/api/v1/tasks",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options.headers,
    },
  });
};

/**
 * Delete task
 * Delete task by ID
 */
export const deleteTask = <ThrowOnError extends boolean = false>(options: Options<DeleteTaskData, ThrowOnError>) => {
  return (options.client ?? _heyApiClient).delete<DeleteTaskResponses, DeleteTaskErrors, ThrowOnError>({
    security: [
      {
        scheme: "bearer",
        type: "http",
      },
    ],
    url: "/api/v1/tasks/{id}",
    ...options,
  });
};

/**
 * Get task
 * Get task by ID
 */
export const getTask = <ThrowOnError extends boolean = false>(options: Options<GetTaskData, ThrowOnError>) => {
  return (options.client ?? _heyApiClient).get<GetTaskResponses, GetTaskErrors, ThrowOnError>({
    security: [
      {
        scheme: "bearer",
        type: "http",
      },
    ],
    url: "/api/v1/tasks/{id}",
    ...options,
  });
};

/**
 * Update task
 * Update task by ID
 */
export const updateTask = <ThrowOnError extends boolean = false>(options: Options<UpdateTaskData, ThrowOnError>) => {
  return (options.client ?? _heyApiClient).put<UpdateTaskResponses, UpdateTaskErrors, ThrowOnError>({
    security: [
      {
        scheme: "bearer",
        type: "http",
      },
    ],
    url: "/api/v1/tasks/{id}",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options.headers,
    },
  });
};

/**
 * Update task status
 * Update the status of the current active task record for the specified task
 */
export const updateTaskStatus = <ThrowOnError extends boolean = false>(
  options: Options<UpdateTaskStatusData, ThrowOnError>
) => {
  return (options.client ?? _heyApiClient).put<UpdateTaskStatusResponses, UpdateTaskStatusErrors, ThrowOnError>({
    security: [
      {
        scheme: "bearer",
        type: "http",
      },
    ],
    url: "/api/v1/tasks/{id}/status",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options.headers,
    },
  });
};

/**
 * Get task history
 * Get paginated history of task records for a task
 */
export const getTaskHistory = <ThrowOnError extends boolean = false>(
  options: Options<GetTaskHistoryData, ThrowOnError>
) => {
  return (options.client ?? _heyApiClient).get<GetTaskHistoryResponses, GetTaskHistoryErrors, ThrowOnError>({
    security: [
      {
        scheme: "bearer",
        type: "http",
      },
    ],
    url: "/api/v1/tasks/{id}/history",
    ...options,
  });
};

/**
 * Remove task transactions
 * Remove specified transactions from the current task record
 */
export const removeTaskTransactions = <ThrowOnError extends boolean = false>(
  options: Options<RemoveTaskTransactionsData, ThrowOnError>
) => {
  return (options.client ?? _heyApiClient).delete<
    RemoveTaskTransactionsResponses,
    RemoveTaskTransactionsErrors,
    ThrowOnError
  >({
    security: [
      {
        scheme: "bearer",
        type: "http",
      },
    ],
    url: "/api/v1/tasks/{id}/transactions",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options.headers,
    },
  });
};

/**
 * Assign transactions to task
 * Assign transactions to the current task record
 */
export const assignTaskTransactions = <ThrowOnError extends boolean = false>(
  options: Options<AssignTaskTransactionsData, ThrowOnError>
) => {
  return (options.client ?? _heyApiClient).post<
    AssignTaskTransactionsResponses,
    AssignTaskTransactionsErrors,
    ThrowOnError
  >({
    security: [
      {
        scheme: "bearer",
        type: "http",
      },
    ],
    url: "/api/v1/tasks/{id}/transactions",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options.headers,
    },
  });
};

/**
 * Replace task transactions
 * Replace all assigned transactions for the current task record
 */
export const replaceTaskTransactions = <ThrowOnError extends boolean = false>(
  options: Options<ReplaceTaskTransactionsData, ThrowOnError>
) => {
  return (options.client ?? _heyApiClient).put<
    ReplaceTaskTransactionsResponses,
    ReplaceTaskTransactionsErrors,
    ThrowOnError
  >({
    security: [
      {
        scheme: "bearer",
        type: "http",
      },
    ],
    url: "/api/v1/tasks/{id}/transactions",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options.headers,
    },
  });
};

/**
 * List transactions
 * Get paginated list of transactions for the authenticated user
 */
export const listTransactions = <ThrowOnError extends boolean = false>(
  options?: Options<ListTransactionsData, ThrowOnError>
) => {
  return (options?.client ?? _heyApiClient).get<ListTransactionsResponses, ListTransactionsErrors, ThrowOnError>({
    security: [
      {
        scheme: "bearer",
        type: "http",
      },
    ],
    url: "/api/v1/transactions",
    ...options,
  });
};

/**
 * Create transaction
 * Create a new transaction
 */
export const createTransaction = <ThrowOnError extends boolean = false>(
  options: Options<CreateTransactionData, ThrowOnError>
) => {
  return (options.client ?? _heyApiClient).post<CreateTransactionResponses, CreateTransactionErrors, ThrowOnError>({
    security: [
      {
        scheme: "bearer",
        type: "http",
      },
    ],
    url: "/api/v1/transactions",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options.headers,
    },
  });
};

/**
 * Delete transaction
 * Delete a transaction
 */
export const deleteTransaction = <ThrowOnError extends boolean = false>(
  options: Options<DeleteTransactionData, ThrowOnError>
) => {
  return (options.client ?? _heyApiClient).delete<DeleteTransactionResponses, DeleteTransactionErrors, ThrowOnError>({
    security: [
      {
        scheme: "bearer",
        type: "http",
      },
    ],
    url: "/api/v1/transactions/{id}",
    ...options,
  });
};

/**
 * Get transaction
 * Get a specific transaction by ID
 */
export const getTransaction = <ThrowOnError extends boolean = false>(
  options: Options<GetTransactionData, ThrowOnError>
) => {
  return (options.client ?? _heyApiClient).get<GetTransactionResponses, GetTransactionErrors, ThrowOnError>({
    security: [
      {
        scheme: "bearer",
        type: "http",
      },
    ],
    url: "/api/v1/transactions/{id}",
    ...options,
  });
};

/**
 * Update transaction
 * Update an existing transaction
 */
export const updateTransaction = <ThrowOnError extends boolean = false>(
  options: Options<UpdateTransactionData, ThrowOnError>
) => {
  return (options.client ?? _heyApiClient).put<UpdateTransactionResponses, UpdateTransactionErrors, ThrowOnError>({
    security: [
      {
        scheme: "bearer",
        type: "http",
      },
    ],
    url: "/api/v1/transactions/{id}",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options.headers,
    },
  });
};
