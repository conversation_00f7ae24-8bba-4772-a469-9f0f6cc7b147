import type { DefaultError, InfiniteData, UseMutationOptions } from "@tanstack/react-query";
import type { Options } from "../sdk.gen";
import type {
  AssignTaskTransactionsData,
  AssignTaskTransactionsError,
  CreateAccountData,
  CreateAccountError,
  CreateAccountGroupData,
  CreateAccountGroupError,
  CreateAccountGroupResponse,
  CreateAccountResponse,
  CreateBudgetData,
  CreateBudgetError,
  CreateBudgetResponse,
  CreateCategoryData,
  CreateCategoryError,
  CreateCategoryResponse,
  CreateGoalData,
  CreateGoalError,
  CreateGoalResponse,
  CreateTaskData,
  CreateTaskError,
  CreateTaskResponse,
  CreateTransactionData,
  CreateTransactionError,
  CreateTransactionResponse,
  CreateUserData,
  CreateUserError,
  CreateUserResponse,
  DeleteAccountData,
  DeleteAccountError,
  DeleteAccountGroupData,
  DeleteAccountGroupError,
  DeleteAccountGroupResponse,
  DeleteAccountResponse,
  DeleteBudgetData,
  DeleteBudgetError,
  DeleteBudgetResponse,
  DeleteCategoryData,
  DeleteCategoryError,
  DeleteCategoryResponse,
  DeleteGoalData,
  DeleteGoalError,
  DeleteGoalResponse,
  DeleteTaskData,
  DeleteTaskError,
  DeleteTaskResponse,
  DeleteTransactionData,
  DeleteTransactionError,
  DeleteTransactionResponse,
  GetAccountData,
  GetAccountGroupData,
  GetBudgetData,
  GetBudgetHistoryData,
  GetBudgetHistoryError,
  GetBudgetHistoryResponse,
  GetCategoryData,
  GetCurrentUserData,
  GetGoalData,
  GetTaskData,
  GetTaskHistoryData,
  GetTaskHistoryError,
  GetTaskHistoryResponse,
  GetTokenData,
  GetTokenError,
  GetTokenResponse,
  GetTransactionData,
  ListAccountGroupsData,
  ListAccountsData,
  ListBudgetsData,
  ListCategoriesData,
  ListGoalsData,
  ListTasksData,
  ListTasksError,
  ListTasksResponse,
  ListTransactionsData,
  ListTransactionsError,
  ListTransactionsResponse,
  LogoutData,
  LogoutResponse,
  RemoveTaskTransactionsData,
  RemoveTaskTransactionsError,
  ReplaceTaskTransactionsData,
  ReplaceTaskTransactionsError,
  UpdateAccountBalanceData,
  UpdateAccountBalanceError,
  UpdateAccountBalanceResponse,
  UpdateAccountData,
  UpdateAccountError,
  UpdateAccountGroupData,
  UpdateAccountGroupError,
  UpdateAccountGroupResponse,
  UpdateAccountResponse,
  UpdateBudgetData,
  UpdateBudgetError,
  UpdateBudgetResponse,
  UpdateCategoryData,
  UpdateCategoryError,
  UpdateCategoryResponse,
  UpdateGoalData,
  UpdateGoalError,
  UpdateGoalResponse,
  UpdateTaskData,
  UpdateTaskError,
  UpdateTaskResponse,
  UpdateTaskStatusData,
  UpdateTaskStatusError,
  UpdateTransactionData,
  UpdateTransactionError,
  UpdateTransactionResponse,
} from "../types.gen";

import { infiniteQueryOptions, queryOptions } from "@tanstack/react-query";

import { client as _heyApiClient } from "../client.gen";
import {
  assignTaskTransactions,
  createAccount,
  createAccountGroup,
  createBudget,
  createCategory,
  createGoal,
  createTask,
  createTransaction,
  createUser,
  deleteAccount,
  deleteAccountGroup,
  deleteBudget,
  deleteCategory,
  deleteGoal,
  deleteTask,
  deleteTransaction,
  getAccount,
  getAccountGroup,
  getBudget,
  getBudgetHistory,
  getCategory,
  getCurrentUser,
  getGoal,
  getTask,
  getTaskHistory,
  getToken,
  getTransaction,
  listAccountGroups,
  listAccounts,
  listBudgets,
  listCategories,
  listGoals,
  listTasks,
  listTransactions,
  logout,
  removeTaskTransactions,
  replaceTaskTransactions,
  updateAccount,
  updateAccountBalance,
  updateAccountGroup,
  updateBudget,
  updateCategory,
  updateGoal,
  updateTask,
  updateTaskStatus,
  updateTransaction,
} from "../sdk.gen";

// This file is auto-generated by @hey-api/openapi-ts

export type QueryKey<TOptions extends Options> = [
  Pick<TOptions, "baseUrl" | "body" | "headers" | "path" | "query"> & {
    _id: string;
    _infinite?: boolean;
  },
];

const createQueryKey = <TOptions extends Options>(
  id: string,
  options?: TOptions,
  infinite?: boolean
): [QueryKey<TOptions>[0]] => {
  const params: QueryKey<TOptions>[0] = {
    _id: id,
    baseUrl: (options?.client ?? _heyApiClient).getConfig().baseUrl,
  } as QueryKey<TOptions>[0];
  if (infinite) {
    params._infinite = infinite;
  }
  if (options?.body) {
    params.body = options.body;
  }
  if (options?.headers) {
    params.headers = options.headers;
  }
  if (options?.path) {
    params.path = options.path;
  }
  if (options?.query) {
    params.query = options.query;
  }
  return [params];
};

export const getTokenQueryKey = (options: Options<GetTokenData>) => createQueryKey("getToken", options);

/**
 * Get access token
 * Get access token for the user. Generated token should be used in the `Authorization` header or `access_token` cookie.
 */
export const getTokenOptions = (options: Options<GetTokenData>) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await getToken({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true,
      });
      return data;
    },
    queryKey: getTokenQueryKey(options),
  });
};

/**
 * Get access token
 * Get access token for the user. Generated token should be used in the `Authorization` header or `access_token` cookie.
 */
export const getTokenMutation = (
  options?: Partial<Options<GetTokenData>>
): UseMutationOptions<GetTokenResponse, GetTokenError, Options<GetTokenData>> => {
  const mutationOptions: UseMutationOptions<GetTokenResponse, GetTokenError, Options<GetTokenData>> = {
    mutationFn: async (localOptions) => {
      const { data } = await getToken({
        ...options,
        ...localOptions,
        throwOnError: true,
      });
      return data;
    },
  };
  return mutationOptions;
};

/**
 * Logout user
 * Logout user by clearing the access token cookie.
 */
export const logoutMutation = (
  options?: Partial<Options<LogoutData>>
): UseMutationOptions<LogoutResponse, DefaultError, Options<LogoutData>> => {
  const mutationOptions: UseMutationOptions<LogoutResponse, DefaultError, Options<LogoutData>> = {
    mutationFn: async (localOptions) => {
      const { data } = await logout({
        ...options,
        ...localOptions,
        throwOnError: true,
      });
      return data;
    },
  };
  return mutationOptions;
};

export const createUserQueryKey = (options: Options<CreateUserData>) => createQueryKey("createUser", options);

/**
 * Create user
 * Create (register) a new user. It returns the created user, but access tokens will not be generated and should be requested separately.
 */
export const createUserOptions = (options: Options<CreateUserData>) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await createUser({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true,
      });
      return data;
    },
    queryKey: createUserQueryKey(options),
  });
};

/**
 * Create user
 * Create (register) a new user. It returns the created user, but access tokens will not be generated and should be requested separately.
 */
export const createUserMutation = (
  options?: Partial<Options<CreateUserData>>
): UseMutationOptions<CreateUserResponse, CreateUserError, Options<CreateUserData>> => {
  const mutationOptions: UseMutationOptions<CreateUserResponse, CreateUserError, Options<CreateUserData>> = {
    mutationFn: async (localOptions) => {
      const { data } = await createUser({
        ...options,
        ...localOptions,
        throwOnError: true,
      });
      return data;
    },
  };
  return mutationOptions;
};

export const getCurrentUserQueryKey = (options?: Options<GetCurrentUserData>) =>
  createQueryKey("getCurrentUser", options);

/**
 * Current user
 * Get current user details
 */
export const getCurrentUserOptions = (options?: Options<GetCurrentUserData>) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await getCurrentUser({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true,
      });
      return data;
    },
    queryKey: getCurrentUserQueryKey(options),
  });
};

export const listAccountGroupsQueryKey = (options?: Options<ListAccountGroupsData>) =>
  createQueryKey("listAccountGroups", options);

/**
 * List account groups
 * Get all account groups for the current user
 */
export const listAccountGroupsOptions = (options?: Options<ListAccountGroupsData>) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await listAccountGroups({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true,
      });
      return data;
    },
    queryKey: listAccountGroupsQueryKey(options),
  });
};

export const createAccountGroupQueryKey = (options: Options<CreateAccountGroupData>) =>
  createQueryKey("createAccountGroup", options);

/**
 * Create account group
 * Create a new account group
 */
export const createAccountGroupOptions = (options: Options<CreateAccountGroupData>) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await createAccountGroup({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true,
      });
      return data;
    },
    queryKey: createAccountGroupQueryKey(options),
  });
};

/**
 * Create account group
 * Create a new account group
 */
export const createAccountGroupMutation = (
  options?: Partial<Options<CreateAccountGroupData>>
): UseMutationOptions<CreateAccountGroupResponse, CreateAccountGroupError, Options<CreateAccountGroupData>> => {
  const mutationOptions: UseMutationOptions<
    CreateAccountGroupResponse,
    CreateAccountGroupError,
    Options<CreateAccountGroupData>
  > = {
    mutationFn: async (localOptions) => {
      const { data } = await createAccountGroup({
        ...options,
        ...localOptions,
        throwOnError: true,
      });
      return data;
    },
  };
  return mutationOptions;
};

/**
 * Delete account group
 * Delete account group by ID
 */
export const deleteAccountGroupMutation = (
  options?: Partial<Options<DeleteAccountGroupData>>
): UseMutationOptions<DeleteAccountGroupResponse, DeleteAccountGroupError, Options<DeleteAccountGroupData>> => {
  const mutationOptions: UseMutationOptions<
    DeleteAccountGroupResponse,
    DeleteAccountGroupError,
    Options<DeleteAccountGroupData>
  > = {
    mutationFn: async (localOptions) => {
      const { data } = await deleteAccountGroup({
        ...options,
        ...localOptions,
        throwOnError: true,
      });
      return data;
    },
  };
  return mutationOptions;
};

export const getAccountGroupQueryKey = (options: Options<GetAccountGroupData>) =>
  createQueryKey("getAccountGroup", options);

/**
 * Get account group
 * Get account group by ID
 */
export const getAccountGroupOptions = (options: Options<GetAccountGroupData>) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await getAccountGroup({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true,
      });
      return data;
    },
    queryKey: getAccountGroupQueryKey(options),
  });
};

/**
 * Update account group
 * Update account group by ID
 */
export const updateAccountGroupMutation = (
  options?: Partial<Options<UpdateAccountGroupData>>
): UseMutationOptions<UpdateAccountGroupResponse, UpdateAccountGroupError, Options<UpdateAccountGroupData>> => {
  const mutationOptions: UseMutationOptions<
    UpdateAccountGroupResponse,
    UpdateAccountGroupError,
    Options<UpdateAccountGroupData>
  > = {
    mutationFn: async (localOptions) => {
      const { data } = await updateAccountGroup({
        ...options,
        ...localOptions,
        throwOnError: true,
      });
      return data;
    },
  };
  return mutationOptions;
};

export const listAccountsQueryKey = (options?: Options<ListAccountsData>) => createQueryKey("listAccounts", options);

/**
 * List accounts
 * Get all accounts for the current user
 */
export const listAccountsOptions = (options?: Options<ListAccountsData>) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await listAccounts({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true,
      });
      return data;
    },
    queryKey: listAccountsQueryKey(options),
  });
};

export const createAccountQueryKey = (options: Options<CreateAccountData>) => createQueryKey("createAccount", options);

/**
 * Create account
 * Create a new account
 */
export const createAccountOptions = (options: Options<CreateAccountData>) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await createAccount({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true,
      });
      return data;
    },
    queryKey: createAccountQueryKey(options),
  });
};

/**
 * Create account
 * Create a new account
 */
export const createAccountMutation = (
  options?: Partial<Options<CreateAccountData>>
): UseMutationOptions<CreateAccountResponse, CreateAccountError, Options<CreateAccountData>> => {
  const mutationOptions: UseMutationOptions<CreateAccountResponse, CreateAccountError, Options<CreateAccountData>> = {
    mutationFn: async (localOptions) => {
      const { data } = await createAccount({
        ...options,
        ...localOptions,
        throwOnError: true,
      });
      return data;
    },
  };
  return mutationOptions;
};

/**
 * Delete account
 * Delete account by ID
 */
export const deleteAccountMutation = (
  options?: Partial<Options<DeleteAccountData>>
): UseMutationOptions<DeleteAccountResponse, DeleteAccountError, Options<DeleteAccountData>> => {
  const mutationOptions: UseMutationOptions<DeleteAccountResponse, DeleteAccountError, Options<DeleteAccountData>> = {
    mutationFn: async (localOptions) => {
      const { data } = await deleteAccount({
        ...options,
        ...localOptions,
        throwOnError: true,
      });
      return data;
    },
  };
  return mutationOptions;
};

export const getAccountQueryKey = (options: Options<GetAccountData>) => createQueryKey("getAccount", options);

/**
 * Get account
 * Get account by ID
 */
export const getAccountOptions = (options: Options<GetAccountData>) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await getAccount({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true,
      });
      return data;
    },
    queryKey: getAccountQueryKey(options),
  });
};

/**
 * Update account
 * Update account by ID
 */
export const updateAccountMutation = (
  options?: Partial<Options<UpdateAccountData>>
): UseMutationOptions<UpdateAccountResponse, UpdateAccountError, Options<UpdateAccountData>> => {
  const mutationOptions: UseMutationOptions<UpdateAccountResponse, UpdateAccountError, Options<UpdateAccountData>> = {
    mutationFn: async (localOptions) => {
      const { data } = await updateAccount({
        ...options,
        ...localOptions,
        throwOnError: true,
      });
      return data;
    },
  };
  return mutationOptions;
};

/**
 * Update account balance
 * Update current balance for an account
 */
export const updateAccountBalanceMutation = (
  options?: Partial<Options<UpdateAccountBalanceData>>
): UseMutationOptions<UpdateAccountBalanceResponse, UpdateAccountBalanceError, Options<UpdateAccountBalanceData>> => {
  const mutationOptions: UseMutationOptions<
    UpdateAccountBalanceResponse,
    UpdateAccountBalanceError,
    Options<UpdateAccountBalanceData>
  > = {
    mutationFn: async (localOptions) => {
      const { data } = await updateAccountBalance({
        ...options,
        ...localOptions,
        throwOnError: true,
      });
      return data;
    },
  };
  return mutationOptions;
};

export const listBudgetsQueryKey = (options?: Options<ListBudgetsData>) => createQueryKey("listBudgets", options);

/**
 * List budgets
 * Get all budgets for the current user
 */
export const listBudgetsOptions = (options?: Options<ListBudgetsData>) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await listBudgets({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true,
      });
      return data;
    },
    queryKey: listBudgetsQueryKey(options),
  });
};

export const createBudgetQueryKey = (options: Options<CreateBudgetData>) => createQueryKey("createBudget", options);

/**
 * Create budget
 * Create a new budget
 */
export const createBudgetOptions = (options: Options<CreateBudgetData>) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await createBudget({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true,
      });
      return data;
    },
    queryKey: createBudgetQueryKey(options),
  });
};

/**
 * Create budget
 * Create a new budget
 */
export const createBudgetMutation = (
  options?: Partial<Options<CreateBudgetData>>
): UseMutationOptions<CreateBudgetResponse, CreateBudgetError, Options<CreateBudgetData>> => {
  const mutationOptions: UseMutationOptions<CreateBudgetResponse, CreateBudgetError, Options<CreateBudgetData>> = {
    mutationFn: async (localOptions) => {
      const { data } = await createBudget({
        ...options,
        ...localOptions,
        throwOnError: true,
      });
      return data;
    },
  };
  return mutationOptions;
};

/**
 * Delete budget
 * Delete budget by ID
 */
export const deleteBudgetMutation = (
  options?: Partial<Options<DeleteBudgetData>>
): UseMutationOptions<DeleteBudgetResponse, DeleteBudgetError, Options<DeleteBudgetData>> => {
  const mutationOptions: UseMutationOptions<DeleteBudgetResponse, DeleteBudgetError, Options<DeleteBudgetData>> = {
    mutationFn: async (localOptions) => {
      const { data } = await deleteBudget({
        ...options,
        ...localOptions,
        throwOnError: true,
      });
      return data;
    },
  };
  return mutationOptions;
};

export const getBudgetQueryKey = (options: Options<GetBudgetData>) => createQueryKey("getBudget", options);

/**
 * Get budget
 * Get budget by ID
 */
export const getBudgetOptions = (options: Options<GetBudgetData>) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await getBudget({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true,
      });
      return data;
    },
    queryKey: getBudgetQueryKey(options),
  });
};

/**
 * Update budget
 * Update budget by ID
 */
export const updateBudgetMutation = (
  options?: Partial<Options<UpdateBudgetData>>
): UseMutationOptions<UpdateBudgetResponse, UpdateBudgetError, Options<UpdateBudgetData>> => {
  const mutationOptions: UseMutationOptions<UpdateBudgetResponse, UpdateBudgetError, Options<UpdateBudgetData>> = {
    mutationFn: async (localOptions) => {
      const { data } = await updateBudget({
        ...options,
        ...localOptions,
        throwOnError: true,
      });
      return data;
    },
  };
  return mutationOptions;
};

export const getBudgetHistoryQueryKey = (options: Options<GetBudgetHistoryData>) =>
  createQueryKey("getBudgetHistory", options);

/**
 * Get budget history
 * Get paginated budget records for a specific budget
 */
export const getBudgetHistoryOptions = (options: Options<GetBudgetHistoryData>) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await getBudgetHistory({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true,
      });
      return data;
    },
    queryKey: getBudgetHistoryQueryKey(options),
  });
};

const createInfiniteParams = <K extends Pick<QueryKey<Options>[0], "body" | "headers" | "path" | "query">>(
  queryKey: QueryKey<Options>,
  page: K
) => {
  const params = {
    ...queryKey[0],
  };
  if (page.body) {
    params.body = {
      ...(queryKey[0].body as any),
      ...(page.body as any),
    };
  }
  if (page.headers) {
    params.headers = {
      ...queryKey[0].headers,
      ...page.headers,
    };
  }
  if (page.path) {
    params.path = {
      ...(queryKey[0].path as any),
      ...(page.path as any),
    };
  }
  if (page.query) {
    params.query = {
      ...(queryKey[0].query as any),
      ...(page.query as any),
    };
  }
  return params as unknown as typeof page;
};

export const getBudgetHistoryInfiniteQueryKey = (
  options: Options<GetBudgetHistoryData>
): QueryKey<Options<GetBudgetHistoryData>> => createQueryKey("getBudgetHistory", options, true);

/**
 * Get budget history
 * Get paginated budget records for a specific budget
 */
export const getBudgetHistoryInfiniteOptions = (options: Options<GetBudgetHistoryData>) => {
  return infiniteQueryOptions<
    GetBudgetHistoryResponse,
    GetBudgetHistoryError,
    InfiniteData<GetBudgetHistoryResponse>,
    QueryKey<Options<GetBudgetHistoryData>>,
    number | Pick<QueryKey<Options<GetBudgetHistoryData>>[0], "body" | "headers" | "path" | "query">
  >(
    // @ts-ignore
    {
      queryFn: async ({ pageParam, queryKey, signal }) => {
        // @ts-ignore
        const page: Pick<QueryKey<Options<GetBudgetHistoryData>>[0], "body" | "headers" | "path" | "query"> =
          typeof pageParam === "object"
            ? pageParam
            : {
                query: {
                  page: pageParam,
                },
              };
        const params = createInfiniteParams(queryKey, page);
        const { data } = await getBudgetHistory({
          ...options,
          ...params,
          signal,
          throwOnError: true,
        });
        return data;
      },
      queryKey: getBudgetHistoryInfiniteQueryKey(options),
    }
  );
};

export const listCategoriesQueryKey = (options?: Options<ListCategoriesData>) =>
  createQueryKey("listCategories", options);

/**
 * List categories
 * Get all categories for the current user
 */
export const listCategoriesOptions = (options?: Options<ListCategoriesData>) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await listCategories({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true,
      });
      return data;
    },
    queryKey: listCategoriesQueryKey(options),
  });
};

export const createCategoryQueryKey = (options: Options<CreateCategoryData>) =>
  createQueryKey("createCategory", options);

/**
 * Create category
 * Create a new category
 */
export const createCategoryOptions = (options: Options<CreateCategoryData>) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await createCategory({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true,
      });
      return data;
    },
    queryKey: createCategoryQueryKey(options),
  });
};

/**
 * Create category
 * Create a new category
 */
export const createCategoryMutation = (
  options?: Partial<Options<CreateCategoryData>>
): UseMutationOptions<CreateCategoryResponse, CreateCategoryError, Options<CreateCategoryData>> => {
  const mutationOptions: UseMutationOptions<
    CreateCategoryResponse,
    CreateCategoryError,
    Options<CreateCategoryData>
  > = {
    mutationFn: async (localOptions) => {
      const { data } = await createCategory({
        ...options,
        ...localOptions,
        throwOnError: true,
      });
      return data;
    },
  };
  return mutationOptions;
};

/**
 * Delete category
 * Delete category by ID
 */
export const deleteCategoryMutation = (
  options?: Partial<Options<DeleteCategoryData>>
): UseMutationOptions<DeleteCategoryResponse, DeleteCategoryError, Options<DeleteCategoryData>> => {
  const mutationOptions: UseMutationOptions<
    DeleteCategoryResponse,
    DeleteCategoryError,
    Options<DeleteCategoryData>
  > = {
    mutationFn: async (localOptions) => {
      const { data } = await deleteCategory({
        ...options,
        ...localOptions,
        throwOnError: true,
      });
      return data;
    },
  };
  return mutationOptions;
};

export const getCategoryQueryKey = (options: Options<GetCategoryData>) => createQueryKey("getCategory", options);

/**
 * Get category
 * Get category by ID
 */
export const getCategoryOptions = (options: Options<GetCategoryData>) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await getCategory({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true,
      });
      return data;
    },
    queryKey: getCategoryQueryKey(options),
  });
};

/**
 * Update category
 * Update category by ID
 */
export const updateCategoryMutation = (
  options?: Partial<Options<UpdateCategoryData>>
): UseMutationOptions<UpdateCategoryResponse, UpdateCategoryError, Options<UpdateCategoryData>> => {
  const mutationOptions: UseMutationOptions<
    UpdateCategoryResponse,
    UpdateCategoryError,
    Options<UpdateCategoryData>
  > = {
    mutationFn: async (localOptions) => {
      const { data } = await updateCategory({
        ...options,
        ...localOptions,
        throwOnError: true,
      });
      return data;
    },
  };
  return mutationOptions;
};

export const listGoalsQueryKey = (options?: Options<ListGoalsData>) => createQueryKey("listGoals", options);

/**
 * List goals
 * Get all financial goals for the current user
 */
export const listGoalsOptions = (options?: Options<ListGoalsData>) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await listGoals({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true,
      });
      return data;
    },
    queryKey: listGoalsQueryKey(options),
  });
};

export const createGoalQueryKey = (options: Options<CreateGoalData>) => createQueryKey("createGoal", options);

/**
 * Create goal
 * Create a new financial goal
 */
export const createGoalOptions = (options: Options<CreateGoalData>) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await createGoal({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true,
      });
      return data;
    },
    queryKey: createGoalQueryKey(options),
  });
};

/**
 * Create goal
 * Create a new financial goal
 */
export const createGoalMutation = (
  options?: Partial<Options<CreateGoalData>>
): UseMutationOptions<CreateGoalResponse, CreateGoalError, Options<CreateGoalData>> => {
  const mutationOptions: UseMutationOptions<CreateGoalResponse, CreateGoalError, Options<CreateGoalData>> = {
    mutationFn: async (localOptions) => {
      const { data } = await createGoal({
        ...options,
        ...localOptions,
        throwOnError: true,
      });
      return data;
    },
  };
  return mutationOptions;
};

/**
 * Delete goal
 * Delete a financial goal
 */
export const deleteGoalMutation = (
  options?: Partial<Options<DeleteGoalData>>
): UseMutationOptions<DeleteGoalResponse, DeleteGoalError, Options<DeleteGoalData>> => {
  const mutationOptions: UseMutationOptions<DeleteGoalResponse, DeleteGoalError, Options<DeleteGoalData>> = {
    mutationFn: async (localOptions) => {
      const { data } = await deleteGoal({
        ...options,
        ...localOptions,
        throwOnError: true,
      });
      return data;
    },
  };
  return mutationOptions;
};

export const getGoalQueryKey = (options: Options<GetGoalData>) => createQueryKey("getGoal", options);

/**
 * Get goal
 * Get a specific financial goal by ID
 */
export const getGoalOptions = (options: Options<GetGoalData>) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await getGoal({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true,
      });
      return data;
    },
    queryKey: getGoalQueryKey(options),
  });
};

/**
 * Update goal
 * Update a financial goal
 */
export const updateGoalMutation = (
  options?: Partial<Options<UpdateGoalData>>
): UseMutationOptions<UpdateGoalResponse, UpdateGoalError, Options<UpdateGoalData>> => {
  const mutationOptions: UseMutationOptions<UpdateGoalResponse, UpdateGoalError, Options<UpdateGoalData>> = {
    mutationFn: async (localOptions) => {
      const { data } = await updateGoal({
        ...options,
        ...localOptions,
        throwOnError: true,
      });
      return data;
    },
  };
  return mutationOptions;
};

export const listTasksQueryKey = (options?: Options<ListTasksData>) => createQueryKey("listTasks", options);

/**
 * List tasks
 * Get all active tasks for the current user
 */
export const listTasksOptions = (options?: Options<ListTasksData>) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await listTasks({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true,
      });
      return data;
    },
    queryKey: listTasksQueryKey(options),
  });
};

export const listTasksInfiniteQueryKey = (options?: Options<ListTasksData>): QueryKey<Options<ListTasksData>> =>
  createQueryKey("listTasks", options, true);

/**
 * List tasks
 * Get all active tasks for the current user
 */
export const listTasksInfiniteOptions = (options?: Options<ListTasksData>) => {
  return infiniteQueryOptions<
    ListTasksResponse,
    ListTasksError,
    InfiniteData<ListTasksResponse>,
    QueryKey<Options<ListTasksData>>,
    number | Pick<QueryKey<Options<ListTasksData>>[0], "body" | "headers" | "path" | "query">
  >(
    // @ts-ignore
    {
      queryFn: async ({ pageParam, queryKey, signal }) => {
        // @ts-ignore
        const page: Pick<QueryKey<Options<ListTasksData>>[0], "body" | "headers" | "path" | "query"> =
          typeof pageParam === "object"
            ? pageParam
            : {
                query: {
                  page: pageParam,
                },
              };
        const params = createInfiniteParams(queryKey, page);
        const { data } = await listTasks({
          ...options,
          ...params,
          signal,
          throwOnError: true,
        });
        return data;
      },
      queryKey: listTasksInfiniteQueryKey(options),
    }
  );
};

export const createTaskQueryKey = (options: Options<CreateTaskData>) => createQueryKey("createTask", options);

/**
 * Create task
 * Create a new recurring task
 */
export const createTaskOptions = (options: Options<CreateTaskData>) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await createTask({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true,
      });
      return data;
    },
    queryKey: createTaskQueryKey(options),
  });
};

/**
 * Create task
 * Create a new recurring task
 */
export const createTaskMutation = (
  options?: Partial<Options<CreateTaskData>>
): UseMutationOptions<CreateTaskResponse, CreateTaskError, Options<CreateTaskData>> => {
  const mutationOptions: UseMutationOptions<CreateTaskResponse, CreateTaskError, Options<CreateTaskData>> = {
    mutationFn: async (localOptions) => {
      const { data } = await createTask({
        ...options,
        ...localOptions,
        throwOnError: true,
      });
      return data;
    },
  };
  return mutationOptions;
};

/**
 * Delete task
 * Delete task by ID
 */
export const deleteTaskMutation = (
  options?: Partial<Options<DeleteTaskData>>
): UseMutationOptions<DeleteTaskResponse, DeleteTaskError, Options<DeleteTaskData>> => {
  const mutationOptions: UseMutationOptions<DeleteTaskResponse, DeleteTaskError, Options<DeleteTaskData>> = {
    mutationFn: async (localOptions) => {
      const { data } = await deleteTask({
        ...options,
        ...localOptions,
        throwOnError: true,
      });
      return data;
    },
  };
  return mutationOptions;
};

export const getTaskQueryKey = (options: Options<GetTaskData>) => createQueryKey("getTask", options);

/**
 * Get task
 * Get task by ID
 */
export const getTaskOptions = (options: Options<GetTaskData>) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await getTask({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true,
      });
      return data;
    },
    queryKey: getTaskQueryKey(options),
  });
};

/**
 * Update task
 * Update task by ID
 */
export const updateTaskMutation = (
  options?: Partial<Options<UpdateTaskData>>
): UseMutationOptions<UpdateTaskResponse, UpdateTaskError, Options<UpdateTaskData>> => {
  const mutationOptions: UseMutationOptions<UpdateTaskResponse, UpdateTaskError, Options<UpdateTaskData>> = {
    mutationFn: async (localOptions) => {
      const { data } = await updateTask({
        ...options,
        ...localOptions,
        throwOnError: true,
      });
      return data;
    },
  };
  return mutationOptions;
};

/**
 * Update task status
 * Update the status of the current active task record for the specified task
 */
export const updateTaskStatusMutation = (
  options?: Partial<Options<UpdateTaskStatusData>>
): UseMutationOptions<unknown, UpdateTaskStatusError, Options<UpdateTaskStatusData>> => {
  const mutationOptions: UseMutationOptions<unknown, UpdateTaskStatusError, Options<UpdateTaskStatusData>> = {
    mutationFn: async (localOptions) => {
      const { data } = await updateTaskStatus({
        ...options,
        ...localOptions,
        throwOnError: true,
      });
      return data;
    },
  };
  return mutationOptions;
};

export const getTaskHistoryQueryKey = (options: Options<GetTaskHistoryData>) =>
  createQueryKey("getTaskHistory", options);

/**
 * Get task history
 * Get paginated history of task records for a task
 */
export const getTaskHistoryOptions = (options: Options<GetTaskHistoryData>) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await getTaskHistory({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true,
      });
      return data;
    },
    queryKey: getTaskHistoryQueryKey(options),
  });
};

export const getTaskHistoryInfiniteQueryKey = (
  options: Options<GetTaskHistoryData>
): QueryKey<Options<GetTaskHistoryData>> => createQueryKey("getTaskHistory", options, true);

/**
 * Get task history
 * Get paginated history of task records for a task
 */
export const getTaskHistoryInfiniteOptions = (options: Options<GetTaskHistoryData>) => {
  return infiniteQueryOptions<
    GetTaskHistoryResponse,
    GetTaskHistoryError,
    InfiniteData<GetTaskHistoryResponse>,
    QueryKey<Options<GetTaskHistoryData>>,
    number | Pick<QueryKey<Options<GetTaskHistoryData>>[0], "body" | "headers" | "path" | "query">
  >(
    // @ts-ignore
    {
      queryFn: async ({ pageParam, queryKey, signal }) => {
        // @ts-ignore
        const page: Pick<QueryKey<Options<GetTaskHistoryData>>[0], "body" | "headers" | "path" | "query"> =
          typeof pageParam === "object"
            ? pageParam
            : {
                query: {
                  page: pageParam,
                },
              };
        const params = createInfiniteParams(queryKey, page);
        const { data } = await getTaskHistory({
          ...options,
          ...params,
          signal,
          throwOnError: true,
        });
        return data;
      },
      queryKey: getTaskHistoryInfiniteQueryKey(options),
    }
  );
};

/**
 * Remove task transactions
 * Remove specified transactions from the current task record
 */
export const removeTaskTransactionsMutation = (
  options?: Partial<Options<RemoveTaskTransactionsData>>
): UseMutationOptions<unknown, RemoveTaskTransactionsError, Options<RemoveTaskTransactionsData>> => {
  const mutationOptions: UseMutationOptions<
    unknown,
    RemoveTaskTransactionsError,
    Options<RemoveTaskTransactionsData>
  > = {
    mutationFn: async (localOptions) => {
      const { data } = await removeTaskTransactions({
        ...options,
        ...localOptions,
        throwOnError: true,
      });
      return data;
    },
  };
  return mutationOptions;
};

export const assignTaskTransactionsQueryKey = (options: Options<AssignTaskTransactionsData>) =>
  createQueryKey("assignTaskTransactions", options);

/**
 * Assign transactions to task
 * Assign transactions to the current task record
 */
export const assignTaskTransactionsOptions = (options: Options<AssignTaskTransactionsData>) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await assignTaskTransactions({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true,
      });
      return data;
    },
    queryKey: assignTaskTransactionsQueryKey(options),
  });
};

/**
 * Assign transactions to task
 * Assign transactions to the current task record
 */
export const assignTaskTransactionsMutation = (
  options?: Partial<Options<AssignTaskTransactionsData>>
): UseMutationOptions<unknown, AssignTaskTransactionsError, Options<AssignTaskTransactionsData>> => {
  const mutationOptions: UseMutationOptions<
    unknown,
    AssignTaskTransactionsError,
    Options<AssignTaskTransactionsData>
  > = {
    mutationFn: async (localOptions) => {
      const { data } = await assignTaskTransactions({
        ...options,
        ...localOptions,
        throwOnError: true,
      });
      return data;
    },
  };
  return mutationOptions;
};

/**
 * Replace task transactions
 * Replace all assigned transactions for the current task record
 */
export const replaceTaskTransactionsMutation = (
  options?: Partial<Options<ReplaceTaskTransactionsData>>
): UseMutationOptions<unknown, ReplaceTaskTransactionsError, Options<ReplaceTaskTransactionsData>> => {
  const mutationOptions: UseMutationOptions<
    unknown,
    ReplaceTaskTransactionsError,
    Options<ReplaceTaskTransactionsData>
  > = {
    mutationFn: async (localOptions) => {
      const { data } = await replaceTaskTransactions({
        ...options,
        ...localOptions,
        throwOnError: true,
      });
      return data;
    },
  };
  return mutationOptions;
};

export const listTransactionsQueryKey = (options?: Options<ListTransactionsData>) =>
  createQueryKey("listTransactions", options);

/**
 * List transactions
 * Get paginated list of transactions for the authenticated user
 */
export const listTransactionsOptions = (options?: Options<ListTransactionsData>) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await listTransactions({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true,
      });
      return data;
    },
    queryKey: listTransactionsQueryKey(options),
  });
};

export const listTransactionsInfiniteQueryKey = (
  options?: Options<ListTransactionsData>
): QueryKey<Options<ListTransactionsData>> => createQueryKey("listTransactions", options, true);

/**
 * List transactions
 * Get paginated list of transactions for the authenticated user
 */
export const listTransactionsInfiniteOptions = (options?: Options<ListTransactionsData>) => {
  return infiniteQueryOptions<
    ListTransactionsResponse,
    ListTransactionsError,
    InfiniteData<ListTransactionsResponse>,
    QueryKey<Options<ListTransactionsData>>,
    number | Pick<QueryKey<Options<ListTransactionsData>>[0], "body" | "headers" | "path" | "query">
  >(
    // @ts-ignore
    {
      queryFn: async ({ pageParam, queryKey, signal }) => {
        // @ts-ignore
        const page: Pick<QueryKey<Options<ListTransactionsData>>[0], "body" | "headers" | "path" | "query"> =
          typeof pageParam === "object"
            ? pageParam
            : {
                query: {
                  page: pageParam,
                },
              };
        const params = createInfiniteParams(queryKey, page);
        const { data } = await listTransactions({
          ...options,
          ...params,
          signal,
          throwOnError: true,
        });
        return data;
      },
      queryKey: listTransactionsInfiniteQueryKey(options),
    }
  );
};

export const createTransactionQueryKey = (options: Options<CreateTransactionData>) =>
  createQueryKey("createTransaction", options);

/**
 * Create transaction
 * Create a new transaction
 */
export const createTransactionOptions = (options: Options<CreateTransactionData>) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await createTransaction({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true,
      });
      return data;
    },
    queryKey: createTransactionQueryKey(options),
  });
};

/**
 * Create transaction
 * Create a new transaction
 */
export const createTransactionMutation = (
  options?: Partial<Options<CreateTransactionData>>
): UseMutationOptions<CreateTransactionResponse, CreateTransactionError, Options<CreateTransactionData>> => {
  const mutationOptions: UseMutationOptions<
    CreateTransactionResponse,
    CreateTransactionError,
    Options<CreateTransactionData>
  > = {
    mutationFn: async (localOptions) => {
      const { data } = await createTransaction({
        ...options,
        ...localOptions,
        throwOnError: true,
      });
      return data;
    },
  };
  return mutationOptions;
};

/**
 * Delete transaction
 * Delete a transaction
 */
export const deleteTransactionMutation = (
  options?: Partial<Options<DeleteTransactionData>>
): UseMutationOptions<DeleteTransactionResponse, DeleteTransactionError, Options<DeleteTransactionData>> => {
  const mutationOptions: UseMutationOptions<
    DeleteTransactionResponse,
    DeleteTransactionError,
    Options<DeleteTransactionData>
  > = {
    mutationFn: async (localOptions) => {
      const { data } = await deleteTransaction({
        ...options,
        ...localOptions,
        throwOnError: true,
      });
      return data;
    },
  };
  return mutationOptions;
};

export const getTransactionQueryKey = (options: Options<GetTransactionData>) =>
  createQueryKey("getTransaction", options);

/**
 * Get transaction
 * Get a specific transaction by ID
 */
export const getTransactionOptions = (options: Options<GetTransactionData>) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await getTransaction({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true,
      });
      return data;
    },
    queryKey: getTransactionQueryKey(options),
  });
};

/**
 * Update transaction
 * Update an existing transaction
 */
export const updateTransactionMutation = (
  options?: Partial<Options<UpdateTransactionData>>
): UseMutationOptions<UpdateTransactionResponse, UpdateTransactionError, Options<UpdateTransactionData>> => {
  const mutationOptions: UseMutationOptions<
    UpdateTransactionResponse,
    UpdateTransactionError,
    Options<UpdateTransactionData>
  > = {
    mutationFn: async (localOptions) => {
      const { data } = await updateTransaction({
        ...options,
        ...localOptions,
        throwOnError: true,
      });
      return data;
    },
  };
  return mutationOptions;
};
