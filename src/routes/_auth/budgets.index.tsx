import { useMemo } from "react";

import { ErrorMessage, PageHeader } from "~/components/blocks";
import { LoadingIndicator } from "~/components/elements";
import { BudgetDialog, BudgetsList } from "~/features/budgets/components";
import { useBudgets } from "~/features/budgets/hooks";

export const Route = createFileRoute({
  component: RouteComponent,
});

function RouteComponent() {
  const { budgets, isLoading, error } = useBudgets({ includeArchived: true });

  const { activeBudgets, archivedBudgets } = useMemo(() => {
    const active = budgets.filter((budget) => !budget.isArchived);
    const archived = budgets.filter((budget) => budget.isArchived);
    return { activeBudgets: active, archivedBudgets: archived };
  }, [budgets]);

  if (isLoading) {
    return (
      <>
        <PageHeader title="Budgets" />
        <LoadingIndicator />
      </>
    );
  }

  if (error) {
    return (
      <>
        <PageHeader title="Budgets" />
        <ErrorMessage title="Failed to load budgets" error={error} />
      </>
    );
  }

  return (
    <>
      <PageHeader title="Budgets" />
      <BudgetsList
        budgets={budgets}
        activeBudgets={activeBudgets}
        archivedBudgets={archivedBudgets}
        isLoading={isLoading}
      />
      <BudgetDialog />
    </>
  );
}
