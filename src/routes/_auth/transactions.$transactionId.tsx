import { useQuery } from "@tanstack/react-query";

import { getTransactionOptions } from "~/api/@tanstack/react-query.gen";
import { ErrorMessage, PageHeader } from "~/components/blocks";
import { LoadingIndicator } from "~/components/elements";
import { TransactionDialog, TransactionDetailsOverview } from "~/features/transactions/components";

export const Route = createFileRoute({
  component: RouteComponent,
});

function RouteComponent() {
  const { transactionId } = Route.useParams();

  // Data fetching
  const transactionQuery = useQuery(getTransactionOptions({ path: { id: transactionId } }));

  const transaction = transactionQuery.data;
  const isLoading = transactionQuery.isLoading;
  const error = transactionQuery.error;

  if (isLoading) {
    return (
      <>
        <PageHeader title="Transaction Details" />
        <LoadingIndicator />
      </>
    );
  }

  if (error) {
    return (
      <>
        <PageHeader title="Transaction Details" />
        <ErrorMessage title="Failed to load transaction" error={error} />
      </>
    );
  }

  if (!transaction) {
    return (
      <>
        <PageHeader title="Transaction Details" />
        <ErrorMessage title="Transaction not found" />
      </>
    );
  }

  return (
    <>
      <PageHeader title="Transaction Details" backLink={{ to: "/transactions" }} />

      <TransactionDetailsOverview transaction={transaction} />

      <TransactionDialog />
    </>
  );
}
