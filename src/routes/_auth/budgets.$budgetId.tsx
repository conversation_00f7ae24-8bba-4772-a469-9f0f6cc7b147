import { useState } from "react";

import { ErrorMessage, PageHeader } from "~/components/blocks";
import { LoadingIndicator } from "~/components/elements";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "~/components/ui/tabs";
import { BudgetDetailsHistory, BudgetDetailsOverview, BudgetDialog } from "~/features/budgets/components";
import { useBudget } from "~/features/budgets/hooks";

export const Route = createFileRoute({
  component: RouteComponent,
});

function RouteComponent() {
  const { budgetId } = Route.useParams();
  const [activeTab, setActiveTab] = useState("overview");

  const { budget, isLoading, error } = useBudget(budgetId);

  if (isLoading) {
    return (
      <>
        <PageHeader title="Budget Details" />
        <LoadingIndicator />
      </>
    );
  }

  if (error) {
    return (
      <>
        <PageHeader title="Budget Details" />
        <ErrorMessage title="Failed to load budget" error={error} />
      </>
    );
  }

  if (!budget) {
    return (
      <>
        <PageHeader title="Budget Details" />
        <ErrorMessage title="Budget not found" error={new Error("The requested budget could not be found.")} />
      </>
    );
  }

  return (
    <>
      <PageHeader title={budget.name} />

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="history">History</TabsTrigger>
        </TabsList>

        <TabsContent value="overview">
          <BudgetDetailsOverview budget={budget} />
        </TabsContent>

        <TabsContent value="history">
          <BudgetDetailsHistory budgetId={budgetId} />
        </TabsContent>
      </Tabs>

      <BudgetDialog />
    </>
  );
}
