import { ErrorMessage, PageHeader } from "~/components/blocks";
import { CategoriesList, CategoryDialog } from "~/features/categories/components";
import { useCategories } from "~/features/categories/hooks";

export const Route = createFileRoute({
  component: RouteComponent,
});

function RouteComponent() {
  // Data fetching
  const { categories, groupedCategories, isLoading, error } = useCategories();

  if (error) {
    return (
      <>
        <PageHeader title="Categories" />
        <ErrorMessage title="Failed to load categories" error={error} />
      </>
    );
  }

  return (
    <>
      <PageHeader title="Categories" />

      <CategoriesList categories={categories} groupedCategories={groupedCategories} isLoading={isLoading} />

      <CategoryDialog />
    </>
  );
}
