import { useQuery } from "@tanstack/react-query";

import { getCategoryOptions } from "~/api/@tanstack/react-query.gen";
import { ErrorMessage, PageHeader } from "~/components/blocks";
import { LoadingIndicator } from "~/components/elements";
import { CategoryDialog, CategoryDetailsOverview, CategoryDetailsTransactions } from "~/features/categories/components";

export const Route = createFileRoute({
  component: RouteComponent,
});

function RouteComponent() {
  const { categoryId } = Route.useParams();

  // Data fetching
  const categoryQuery = useQuery(getCategoryOptions({ path: { id: categoryId } }));

  const category = categoryQuery.data;
  const isLoading = categoryQuery.isLoading;
  const error = categoryQuery.error;

  if (isLoading) {
    return (
      <>
        <PageHeader title="Category Details" />
        <LoadingIndicator />
      </>
    );
  }

  if (error) {
    return (
      <>
        <PageHeader title="Category Details" />
        <ErrorMessage title="Failed to load category" error={error} />
      </>
    );
  }

  if (!category) {
    return (
      <>
        <PageHeader title="Category Details" />
        <ErrorMessage title="Category not found" />
      </>
    );
  }

  return (
    <>
      <PageHeader title="Category Details" backLink={{ to: "/categories" }} />

      <CategoryDetailsOverview category={category} />

      <CategoryDetailsTransactions className="mt-8" category={category} />

      <CategoryDialog />
    </>
  );
}
