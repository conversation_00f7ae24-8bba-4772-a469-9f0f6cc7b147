/* eslint-disable @typescript-eslint/only-throw-error */
import { Outlet, redirect } from "@tanstack/react-router";

import { MobileHeader, Sidebar } from "~/components/layouts";

export const Route = createFileRoute({
  component: RouteComponent,
  beforeLoad: ({ context }) => {
    if (!context.isAuthenticated) {
      throw redirect({ to: "/login", search: { next: location.pathname } });
    }
  },
});

function RouteComponent() {
  return (
    <div className="mx-auto grid min-h-svh w-full max-w-[calc(16rem+var(--container-5xl))] grid-cols-1 lg:grid-cols-[16rem_1fr]">
      <Sidebar className="hidden lg:flex" />
      <MobileHeader />

      <main className="px-4 lg:px-8">
        <Outlet />
      </main>
    </div>
  );
}
