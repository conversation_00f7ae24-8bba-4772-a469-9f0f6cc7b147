/* eslint-disable @typescript-eslint/only-throw-error */
import { Outlet, redirect } from "@tanstack/react-router";
import { z } from "zod/v4";

export const Route = createFileRoute({
  component: RouteComponent,
  validateSearch: z.object({ next: z.string().optional() }),
  beforeLoad: ({ context, search }) => {
    if (context.isAuthenticated) {
      throw redirect({ to: search.next ?? "/" });
    }
  },
});

function RouteComponent() {
  return (
    <div className="mx-auto flex h-full min-h-svh w-full max-w-md flex-col items-center justify-center p-4">
      <div className="m-2">
        <img src="/logo.webp" alt="Finanze.Pro" className="aspect-square h-12" />
      </div>

      <main className="mt-2">
        <Outlet />
      </main>
    </div>
  );
}
