import type { LinkProps } from "@tanstack/react-router";

import { <PERSON> } from "@tanstack/react-router";
import { ArrowLeftIcon } from "lucide-react";

interface Props {
  title: React.ReactNode;
  children?: React.ReactNode;
  backLink?: LinkProps;
}

export default function PageHeader({ title, children, backLink }: Props) {
  return (
    <div className="my-8 flex flex-col gap-2">
      <div className="flex items-center gap-2.5">
        {backLink && (
          <Link {...backLink} className="text-primary hover:text-primary/90 block">
            <ArrowLeftIcon className="size-8 stroke-3" />
          </Link>
        )}
        <h1 className="text-foreground text-[32px] leading-9 font-extrabold">{title}</h1>
      </div>
      {children}
    </div>
  );
}
