import { cn } from "~/lib/utils";

type Props = React.PropsWithChildren<{ title: string; size?: "sm" | "default" }>;

export default function DefinitionBlock({ title, children, size = "default" }: Props) {
  return (
    <dl className="flex flex-col gap-1">
      <dt className="text-gray text-xs/5 font-semibold uppercase">{title}</dt>
      <dd className={cn("text-foreground font-bold", { "text-sm/6": size === "sm", "text-2xl/8": size === "default" })}>
        {children}
      </dd>
    </dl>
  );
}
