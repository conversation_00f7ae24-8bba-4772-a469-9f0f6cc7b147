import type { CommonError, ValidationError } from "~/api/types.gen";

import { useMemo } from "react";

import { AlertCircleIcon } from "lucide-react";

import { Alert, AlertDescription, AlertTitle } from "~/components/ui/alert";

interface Props {
  title: string;
  error?: Error | CommonError | ValidationError;
}

export default function ErrorMessage({ title, error }: Props) {
  const description = useMemo(() => {
    if (!error) return undefined;

    if ("stack" in error) {
      console.debug("Validation error stack:", error.stack);
    }

    return (
      <>
        <p>{error.message}</p>
        {"errors" in error && (
          <ul>
            {error.errors.map((e, i) => (
              <li key={i}>{e}</li>
            ))}
          </ul>
        )}
      </>
    );
  }, [error]);

  return (
    <Alert variant="warning">
      <AlertCircleIcon />
      <AlertTitle>{title}</AlertTitle>
      {description && <AlertDescription>{description}</AlertDescription>}
    </Alert>
  );
}
