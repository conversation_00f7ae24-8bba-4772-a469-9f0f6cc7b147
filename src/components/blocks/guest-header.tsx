import { cn } from "~/lib/utils";

type Props = Omit<React.ComponentProps<"div">, "title" | "description"> & {
  title: string;
  description: string;
};

export default function GuestHeader({ title, description, className, ...props }: Props) {
  return (
    <div className={cn("flex flex-col items-center gap-2", className)} {...props}>
      <h1 className="text-foreground text-[32px] leading-10 font-bold">{title}</h1>
      <p className="text-center text-sm text-gray-500">{description}</p>
    </div>
  );
}
