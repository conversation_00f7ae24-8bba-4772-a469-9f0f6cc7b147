import type { Control, FieldPath, FieldValues } from "react-hook-form";

import { FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "~/components/ui/form";
import { MultiSelect } from "~/components/ui/multi-select";

type ControlProps<TFieldValues extends FieldValues> = {
  control: Control<TFieldValues>;
  name: FieldPath<TFieldValues>;
};

type ComponentProps = {
  label: string;
  description?: React.ReactNode;
  hint?: React.ReactNode;
  placeholder?: string;
  className?: string;
};

type Props<TFieldValues extends FieldValues> = ControlProps<TFieldValues> &
  ComponentProps &
  Omit<React.ComponentPropsWithoutRef<typeof MultiSelect>, keyof ComponentProps | "name" | "onValueChange">;

export default function InputMultiSelect<TFieldValues extends FieldValues>(props: Props<TFieldValues>) {
  const { options, label, control, name, className, hint, description, placeholder, ...rest } = props;

  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => (
        <FormItem className={className}>
          <div className="flex items-center justify-between gap-2">
            <FormLabel>{label}</FormLabel>
            {typeof hint === "string" ? <span className="text-muted-foreground text-xs">{hint}</span> : hint}
          </div>

          <FormControl>
            <MultiSelect
              options={options}
              onValueChange={field.onChange}
              defaultValue={field.value}
              name={field.name}
              disabled={field.disabled}
              placeholder={placeholder}
              {...rest}
            />
          </FormControl>

          {description && <FormDescription>{description}</FormDescription>}
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
