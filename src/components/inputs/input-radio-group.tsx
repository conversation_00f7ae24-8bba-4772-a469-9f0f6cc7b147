import type { Control, FieldPath, FieldValues } from "react-hook-form";

import { FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "~/components/ui/form";
import { Label } from "~/components/ui/label";
import { RadioGroup, RadioGroupItem } from "~/components/ui/radio-group";

type ControlProps<TFieldValues extends FieldValues> = {
  control: Control<TFieldValues>;
  name: FieldPath<TFieldValues>;
};

type Option = {
  value: string;
  label: string;
};

type ComponentProps = {
  options: readonly Option[];
  label: string;
  description?: React.ReactNode;
  hint?: React.ReactNode;
  className?: string;
};

type Props<TFieldValues extends FieldValues> = ControlProps<TFieldValues> &
  ComponentProps &
  Omit<React.ComponentPropsWithoutRef<typeof RadioGroup>, keyof ComponentProps | "name" | "onValueChange">;

export default function InputRadioGroup<TFieldValues extends FieldValues>(props: Props<TFieldValues>) {
  const { options, label, control, name, className, hint, description, ...rest } = props;

  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => (
        <FormItem className={className}>
          <div className="flex items-center justify-between gap-2">
            <FormLabel>{label}</FormLabel>
            {typeof hint === "string" ? <span className="text-muted-foreground text-xs">{hint}</span> : hint}
          </div>

          <FormControl>
            <RadioGroup
              onValueChange={field.onChange}
              value={field.value}
              name={field.name}
              disabled={field.disabled}
              {...rest}
            >
              {options.map((option) => (
                <div key={option.value} className="flex items-center space-x-2">
                  <RadioGroupItem value={option.value} id={`${field.name}-${option.value}`} />
                  <Label htmlFor={`${field.name}-${option.value}`} className="text-sm font-normal">
                    {option.label}
                  </Label>
                </div>
              ))}
            </RadioGroup>
          </FormControl>

          {description && <FormDescription>{description}</FormDescription>}
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
