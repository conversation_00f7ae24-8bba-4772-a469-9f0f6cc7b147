import { useState } from "react";

import { VisuallyHidden } from "@radix-ui/react-visually-hidden";
import { Link } from "@tanstack/react-router";
import { MenuIcon } from "lucide-react";

import { Button } from "~/components/ui/button";
import { Sheet, SheetContent, SheetTitle } from "~/components/ui/sheet";

import Sidebar from "./sidebar";

export default function MobileHeader() {
  const [open, setOpen] = useState(false);

  return (
    <div className="block bg-white p-4 lg:hidden">
      <div className="flex items-center gap-4">
        <Button variant="ghost" size="icon" onClick={() => setOpen(true)}>
          <MenuIcon className="size-6" />
        </Button>

        <Link to="/">
          <img src="/logo.webp" className="aspect-square h-11" />
        </Link>
      </div>

      <Sheet open={open} onOpenChange={setOpen}>
        <SheetContent aria-describedby={undefined} side="left">
          <VisuallyHidden>
            <SheetTitle>Main Navigation</SheetTitle>
          </VisuallyHidden>

          <Sidebar />
        </SheetContent>
      </Sheet>
    </div>
  );
}
