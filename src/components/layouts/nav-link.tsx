import type { LinkProps } from "@tanstack/react-router";

import { Link } from "@tanstack/react-router";

export default function NavLink(props: LinkProps) {
  return (
    <Link
      className="block border-b-8 px-8 pt-6 pb-4 text-center text-sm leading-5 font-semibold uppercase"
      inactiveProps={{ className: "border-b-white text-gray-600 hover:border-b-accent" }}
      activeProps={{ className: "border-b-primary text-foreground" }}
      {...props}
    />
  );
}
