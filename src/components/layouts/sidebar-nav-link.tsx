import type { LinkProps } from "@tanstack/react-router";

import { Link } from "@tanstack/react-router";

export default function SidebarNavLink(props: LinkProps) {
  return (
    <Link
      className="flex items-center border-l-4 py-1.5 pl-4 text-left text-base font-medium"
      inactiveProps={{ className: "border-l-white text-gray-600 hover:text-foreground hover:border-l-accent" }}
      activeProps={{ className: "border-l-primary text-primary" }}
      {...props}
    />
  );
}
