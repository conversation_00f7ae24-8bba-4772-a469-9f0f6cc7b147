import { createRouter } from "@tanstack/react-router";

import queryClient from "./query-client";
import { routeTree } from "./routeTree.gen";

const router = createRouter({ routeTree, context: { isAuthenticated: undefined!, queryClient } });

// Register the router instance for type safety
declare module "@tanstack/react-router" {
  interface Register {
    router: typeof router;
  }
}

export default router;
