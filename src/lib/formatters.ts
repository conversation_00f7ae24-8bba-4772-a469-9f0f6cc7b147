import { format } from "date-fns";

export function formatCurrency(currency: string, value: string, options?: Intl.NumberFormatOptions) {
  return new Intl.NumberFormat(undefined, {
    style: "currency",
    currency,
    currencyDisplay: currency === "USD" ? "narrowSymbol" : "symbol",
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
    ...options,
  }).format(Number(value));
}

export function formatDate(date: string | Date) {
  return format(date, "dd MMM, yyyy");
}
