import type { Category, TransactionResponse, TransactionType } from "~/api/types.gen";

/**
 * Filters categories by transaction type
 * For income transactions, show income categories (isExpense: false)
 * For expense transactions, show expense categories (isExpense: true)
 * For transfer transactions, show no categories (transfers don't have categories)
 */
export function filterCategoriesByTransactionType(
  categories: Category[],
  transactionType: TransactionType
): Category[] {
  if (transactionType === "transfer") {
    return []; // Transfers don't have categories
  }

  const isExpenseType = transactionType === "expense";
  return categories.filter((category) => category.isExpense === isExpenseType);
}

/**
 * Filters transactions by search query
 */
export function filterTransactionsBySearch(
  transactions: TransactionResponse[],
  searchQuery: string
): TransactionResponse[] {
  if (!searchQuery.trim()) {
    return transactions;
  }

  const query = searchQuery.toLowerCase();
  return transactions.filter((transaction) => {
    // Search in description
    if (transaction.description?.toLowerCase().includes(query)) {
      return true;
    }

    // Search in account name
    if (transaction.account.name.toLowerCase().includes(query)) {
      return true;
    }

    // Search in account group name
    if (transaction.account.group?.name.toLowerCase().includes(query)) {
      return true;
    }

    // Search in destination account name (for transfers)
    if (transaction.accountTo?.name.toLowerCase().includes(query)) {
      return true;
    }

    // Search in destination account group name (for transfers)
    if (transaction.accountTo?.group?.name.toLowerCase().includes(query)) {
      return true;
    }

    // Search in category name
    if (transaction.category?.name.toLowerCase().includes(query)) {
      return true;
    }

    // Search in amount
    if (transaction.amount.includes(query)) {
      return true;
    }

    return false;
  });
}

/**
 * Filters transactions by type
 */
export function filterTransactionsByType(
  transactions: TransactionResponse[],
  type: TransactionType | "all"
): TransactionResponse[] {
  if (type === "all") {
    return transactions;
  }

  return transactions.filter((transaction) => transaction.type === type);
}
