import type { TransactionType } from "~/api/types.gen";

// Transaction type options for forms
export const transactionTypeOptions = [
  { value: "income" as TransactionType, label: "Income", icon: "📈", color: "text-emerald-600" },
  { value: "expense" as TransactionType, label: "Expense", icon: "📉", color: "text-rose-600" },
  { value: "transfer" as TransactionType, label: "Transfer", icon: "🔄", color: "text-gray-600" },
] as const;

// Transaction type labels
export const transactionTypeLabels: Record<TransactionType, string> = {
  income: "Income",
  expense: "Expense",
  transfer: "Transfer",
};

// Transaction type icons
export const transactionTypeIcons: Record<TransactionType, string> = {
  income: "📈",
  expense: "📉",
  transfer: "🔄",
};

// Transaction type colors for styling
export const transactionTypeColors: Record<TransactionType, string> = {
  income: "text-emerald-600",
  expense: "text-rose-600",
  transfer: "text-gray-600",
};

// Default pagination settings
export const DEFAULT_PAGE_SIZE = 20;
export const MAX_PAGE_SIZE = 100;
