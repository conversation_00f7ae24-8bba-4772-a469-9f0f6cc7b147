import type { TransactionResponse } from "~/api/types.gen";

export interface TransactionDialogState {
  isOpen: boolean;
  editingTransaction: TransactionResponse | null;
}

export interface TransactionActionsState {
  transactionDialog: TransactionDialogState;
}

export interface TransactionActionsActions {
  // Transaction dialog actions
  openCreateTransactionDialog: () => void;
  openEditTransactionDialog: (transaction: TransactionResponse) => void;
  closeTransactionDialog: () => void;
}

export type TransactionActionsStore = TransactionActionsState & TransactionActionsActions;
