import type { TransactionResponse } from "~/api/types.gen";
import type { TransactionActionsStore } from "./types";

import { create } from "zustand";
import { devtools } from "zustand/middleware";
import { immer } from "zustand/middleware/immer";

const useTransactionActionsStore = create<TransactionActionsStore>()(
  devtools(
    immer((set) => ({
      // Initial state
      transactionDialog: {
        isOpen: false,
        editingTransaction: null,
      },

      // Transaction dialog actions
      openCreateTransactionDialog() {
        set(
          (state) => {
            state.transactionDialog.isOpen = true;
            state.transactionDialog.editingTransaction = null;
          },
          undefined,
          "transactions/openCreateTransactionDialog"
        );
      },

      openEditTransactionDialog(transaction: TransactionResponse) {
        set(
          (state) => {
            state.transactionDialog.isOpen = true;
            state.transactionDialog.editingTransaction = transaction;
          },
          undefined,
          "transactions/openEditTransactionDialog"
        );
      },

      closeTransactionDialog() {
        set(
          (state) => {
            state.transactionDialog.isOpen = false;
            state.transactionDialog.editingTransaction = null;
          },
          undefined,
          "transactions/closeTransactionDialog"
        );
      },
    })),
    {
      name: "transactionActionsStore",
    }
  )
);

export default useTransactionActionsStore;
