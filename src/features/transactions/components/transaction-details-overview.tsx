import type { Transaction } from "../types";

import {
  ArrowRightLeftIcon,
  BanknoteArrowDownIcon,
  BanknoteArrowUpIcon,
  Edit2Icon,
  MoreHorizontalIcon,
  Trash2Icon,
} from "lucide-react";

import { Box, DefinitionBlock } from "~/components/blocks";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "~/components/ui/dropdown-menu";
import { getAccountName } from "~/features/accounts/utils";
import { useBaseCurrency } from "~/features/auth/hooks";
import { CategoryIcon } from "~/features/categories/components";
import { defaultColor as categoryDefaultColor } from "~/features/categories/constants";
import { formatCurrency, formatDate } from "~/lib/formatters";
import { cn } from "~/lib/utils";

import { useTransactionActions } from "../hooks";

interface Props {
  transaction: Transaction;
}

export default function TransactionDetailsOverview({ transaction }: Props) {
  const baseCurrency = useBaseCurrency();

  const { editTransaction, deleteTransaction } = useTransactionActions();

  const isExpense = transaction.type === "expense";
  const isIncome = transaction.type === "income";
  const isTransfer = transaction.type === "transfer";

  return (
    <Box className="flex flex-col gap-6 ps-8 pe-4 pt-4 pb-8">
      <div className="flex items-center gap-4">
        <CategoryIcon category={transaction.category ?? null} className="size-4" />
        <p className="text-foreground text-xs/5 font-semibold uppercase" title="Created at">
          {formatDate(transaction.transactionDate)}
        </p>
        {isExpense && (
          <p className="text-red flex items-center gap-1 text-sm/5">
            <BanknoteArrowDownIcon className="size-4" />
            Expense
          </p>
        )}
        {isIncome && (
          <p className="text-green flex items-center gap-1 text-sm/5">
            <BanknoteArrowUpIcon className="size-4" />
            Income
          </p>
        )}
        {isTransfer && (
          <p className="flex items-center gap-1 text-sm/5 text-gray-600">
            <ArrowRightLeftIcon className="size-4" />
            Transfer between accounts
          </p>
        )}

        <DropdownMenu>
          <DropdownMenuTrigger className="ms-auto">
            <MoreHorizontalIcon className="text-gray hover:bg-gray/5 size-5 cursor-pointer rounded-xs" />
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={() => editTransaction(transaction)}>
              <Edit2Icon className="mr-1 inline-block size-3" />
              Edit transaction
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => deleteTransaction(transaction)} variant="destructive">
              <Trash2Icon className="mr-1 inline-block size-3" />
              Delete
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      <div>
        <h2 className="text-foreground text-3xl/8 font-bold">{getAccountName(transaction.account)}</h2>
        {transaction.type === "transfer" && transaction.accountTo && (
          <p className="text-gray text-xl/6 font-semibold">→ {getAccountName(transaction.accountTo)}</p>
        )}
        {transaction.description && (
          <p className="mt-1 line-clamp-2 text-base text-gray-600">{transaction.description}</p>
        )}
      </div>

      <div className="flex gap-12">
        <DefinitionBlock title={isTransfer ? "Amount from" : isExpense ? "Amount spent" : "Amount received"}>
          <p className="flex items-baseline gap-2">
            <span
              className={cn({
                "text-emerald-500": isIncome,
                "text-rose-500": isExpense,
              })}
            >
              {formatCurrency(transaction.account.currency, (isIncome ? "+" : "-") + transaction.amount, {
                signDisplay: "always",
              })}
            </span>
            {transaction.account.currency !== baseCurrency && !isTransfer && (
              <span className="text-gray text-base font-normal">
                ({formatCurrency(baseCurrency, transaction.baseAmount)})
              </span>
            )}
          </p>
        </DefinitionBlock>

        {isTransfer && (
          <DefinitionBlock title="Amount to">
            <p className="flex items-baseline gap-2">
              <span
                className={cn({
                  "text-emerald-500": isIncome,
                  "text-rose-500": isExpense,
                })}
              >
                {formatCurrency(transaction.accountTo!.currency, transaction.amountTo, {
                  signDisplay: "always",
                })}
              </span>
            </p>
          </DefinitionBlock>
        )}

        {isTransfer && transaction.account.currency !== transaction.accountTo!.currency && (
          <DefinitionBlock title="Currency rate">
            <p className="flex items-baseline gap-2">
              <span className="text-gray-600">
                {`1 ${transaction.account.currency} = ${(parseFloat(transaction.amountTo) / parseFloat(transaction.amount)).toFixed(4)} ${transaction.accountTo!.currency}`}
              </span>
              <span className="text-gray text-sm">
                (
                {`1 ${transaction.accountTo!.currency} = ${(parseFloat(transaction.amount) / parseFloat(transaction.amountTo)).toFixed(4)} ${transaction.account.currency}`}
                )
              </span>
            </p>
          </DefinitionBlock>
        )}
      </div>

      {transaction.category && (
        <div>
          <p
            className="border-l-4 ps-1.5 text-base font-semibold uppercase"
            style={{ borderColor: transaction.category.color || categoryDefaultColor }}
          >
            <span className="inline-block pt-0.5">{transaction.category.name}</span>
          </p>
        </div>
      )}
    </Box>
  );
}
