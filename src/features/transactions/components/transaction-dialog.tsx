import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "~/components/ui/dialog";

import { useTransactionActionsStore } from "../store";
import TransactionFormCreate from "./transaction-form-create";
import TransactionFormUpdate from "./transaction-form-update";

export default function TransactionDialog() {
  const { transactionDialog, closeTransactionDialog } = useTransactionActionsStore();

  const isOpen = transactionDialog.isOpen;
  const isEditing = !!transactionDialog.editingTransaction;
  const transaction = transactionDialog.editingTransaction;

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && closeTransactionDialog()}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>
            {isEditing ? "Edit Transaction" : "Create Transaction"}
          </DialogTitle>
          <DialogDescription>
            {isEditing
              ? "Update the transaction details below."
              : "Add a new transaction to track your income, expenses, or transfers."}
          </DialogDescription>
        </DialogHeader>

        {isEditing && transaction ? (
          <TransactionFormUpdate transaction={transaction} />
        ) : (
          <TransactionFormCreate />
        )}
      </DialogContent>
    </Dialog>
  );
}
