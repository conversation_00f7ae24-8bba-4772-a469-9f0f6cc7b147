import type { TransactionResponse, TransactionUpdateRequest } from "~/api/types.gen";

import { useEffect, useMemo } from "react";
import { useForm } from "react-hook-form";

import { zodResolver } from "@hookform/resolvers/zod";
import { LoaderIcon } from "lucide-react";
import { z } from "zod";

import { zTransactionUpdateRequest } from "~/api/zod.gen";
import { InputSelect, InputText, InputTextarea } from "~/components/inputs";
import { Button } from "~/components/ui/button";
import { DialogFooter } from "~/components/ui/dialog";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "~/components/ui/form";
import { Input } from "~/components/ui/input";
import { useAccounts } from "~/features/accounts/hooks";
import { useCategories } from "~/features/categories/hooks";

import { transactionTypeOptions } from "../constants";
import { useUpdateTransaction } from "../hooks";
import { useTransactionActionsStore } from "../store";
import { filterCategoriesByTransactionType } from "../utils";

interface Props {
  transaction: TransactionResponse;
}

export default function TransactionFormUpdate({ transaction }: Props) {
  const { closeTransactionDialog } = useTransactionActionsStore();
  const { updateTransaction, isLoading } = useUpdateTransaction();

  const { accounts } = useAccounts();
  const { categories } = useCategories();

  const defaultValues = useMemo<TransactionUpdateRequest>(
    () => ({
      transactionDate: transaction.transactionDate.split("T")[0], // Convert to YYYY-MM-DD format
      type: transaction.type,
      accountId: transaction.accountId,
      amount: transaction.amount,
      categoryId: transaction.categoryId || "",
      description: transaction.description || "",
      accountToId: transaction.accountToId || "",
      amountTo: transaction.amountTo || "0.00",
    }),
    [transaction]
  );

  const form = useForm<TransactionUpdateRequest>({
    resolver: zodResolver(
      zTransactionUpdateRequest.merge(
        z.object({
          categoryId: z
            .union([z.string().uuid(), z.literal(""), z.null()])
            .optional()
            .transform((v) => v || null),
          accountToId: z
            .union([z.string().uuid(), z.literal(""), z.null()])
            .optional()
            .transform((v) => v || null),
        })
      )
    ),
    defaultValues,
  });

  const watchedType = form.watch("type", transaction.type);
  const watchedAccountId = form.watch("accountId", transaction.accountId);

  // Reset form when transaction changes
  useEffect(() => {
    form.reset(defaultValues);
  }, [defaultValues, form]);

  // Clear category when type changes to transfer
  useEffect(() => {
    if (watchedType === "transfer") {
      form.setValue("categoryId", "");
    }
  }, [watchedType, form]);

  // Clear destination account fields when type is not transfer
  useEffect(() => {
    if (watchedType !== "transfer") {
      form.setValue("accountToId", "");
      form.setValue("amountTo", "");
    }
  }, [watchedType, form]);

  const handleSubmit = form.handleSubmit((data: TransactionUpdateRequest) => {
    // Convert date string to ISO format if provided
    const processedData = {
      ...data,
      description: data.description?.trim() || null,
      ...(data.type !== "transfer" ? { accountToId: undefined, amountTo: undefined } : {}),
    };

    updateTransaction(transaction.id, processedData);
  });

  // Filter categories by transaction type
  const filteredCategories = filterCategoriesByTransactionType(categories || [], watchedType!);

  // Filter accounts for destination (exclude source account)
  const destinationAccounts = (accounts || []).filter((account) => account.id !== watchedAccountId);

  return (
    <Form {...form}>
      <form onSubmit={handleSubmit} className="space-y-4">
        {/* Date and Type */}
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <FormField
            control={form.control}
            name="transactionDate"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Transaction Date</FormLabel>
                <FormControl>
                  <Input type="date" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <InputSelect
            values={transactionTypeOptions.map((option) => ({
              value: option.value,
              label: `${option.icon} ${option.label}`,
            }))}
            control={form.control}
            name="type"
            label="Transaction Type"
            placeholder="Select transaction type"
          />
        </div>

        {/* Account and Amount */}
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <InputSelect
            values={(accounts || []).map((account) => ({
              value: account.id,
              label: `${account.name} (${account.currency})`,
            }))}
            control={form.control}
            name="accountId"
            label="Source Account"
            placeholder="Select account"
          />

          <InputText control={form.control} name="amount" label="Amount" type="number" step="0.01" placeholder="0.00" />
        </div>

        {/* Transfer destination fields */}
        {watchedType === "transfer" && (
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            <InputSelect
              values={(destinationAccounts || []).map((account) => ({
                value: account.id,
                label: `${account.name} (${account.currency})`,
              }))}
              control={form.control}
              name="accountToId"
              label="Destination Account"
              placeholder="Select destination account"
            />

            <InputText
              control={form.control}
              name="amountTo"
              label="Destination Amount"
              hint="Optional"
              type="number"
              step="0.01"
              placeholder="Auto-calculated if same currency"
            />
          </div>
        )}

        {/* Category (only for income/expense) */}
        {watchedType !== "transfer" && (
          <InputSelect
            values={filteredCategories.map((category) => ({
              value: category.id,
              label: category.name,
            }))}
            control={form.control}
            name="categoryId"
            label="Category"
            hint="Optional"
            placeholder="Select category"
            clearable
          />
        )}

        {/* Description */}
        <InputTextarea
          control={form.control}
          name="description"
          label="Description"
          hint="Optional"
          placeholder="Add a note about this transaction..."
          className="resize-none"
        />

        <DialogFooter>
          <Button type="submit" disabled={isLoading}>
            {isLoading ? <LoaderIcon className="animate-spin" /> : "Update Transaction"}
          </Button>
          <Button type="button" variant="outline" onClick={closeTransactionDialog} disabled={isLoading}>
            Cancel
          </Button>
        </DialogFooter>
      </form>
    </Form>
  );
}
