import type { Transaction } from "../types";

import { Link } from "@tanstack/react-router";
import { Edit2Icon, MoreHorizontalIcon, ReceiptTextIcon, Trash2Icon } from "lucide-react";

import { Box } from "~/components/blocks";
import { Button } from "~/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLinkItem,
  DropdownMenuTrigger,
} from "~/components/ui/dropdown-menu";
import { getAccountName } from "~/features/accounts/utils";
import { useBaseCurrency } from "~/features/auth/hooks";
import { CategoryIcon } from "~/features/categories/components";
import { defaultColor as categoryDefaultColor } from "~/features/categories/constants";
import { formatCurrency, formatDate } from "~/lib/formatters";

import { useTransactionActions } from "../hooks";

interface Props {
  transaction: Transaction;
}

export default function TransactionRow({ transaction }: Props) {
  const baseCurrency = useBaseCurrency();

  const { editTransaction, deleteTransaction } = useTransactionActions();

  const isTransfer = transaction.type === "transfer";
  const isNonTransfer = transaction.type !== "transfer";

  return (
    <Box className="flex items-center p-0">
      <div className="px-6 py-4">
        <CategoryIcon category={transaction.category ?? null} className="size-5 stroke-[1.5]" />
      </div>
      <div className="px-6 py-4 text-sm/5">{formatDate(transaction.transactionDate)}</div>
      <div className="flex-grow px-6 py-4">
        <Link
          to="/transactions/$transactionId"
          params={{ transactionId: transaction.id }}
          className="text-link hover:text-link/90 block text-sm/5 hover:underline"
        >
          {getAccountName(transaction.account)}
        </Link>
        {isTransfer && <p className="text-gray text-sm/5">→ {getAccountName(transaction.accountTo!)}</p>}
        {transaction.description && <p className="text-gray text-xs/5">{transaction.description}</p>}
      </div>

      <div className="min-w-52 px-6 py-4">
        {transaction.category && (
          <p
            className="border-l-2 ps-1.5 text-xs/4 font-semibold uppercase"
            style={{ borderColor: transaction.category.color || categoryDefaultColor }}
          >
            <span className="inline-block pt-0.5">{transaction.category.name}</span>
          </p>
        )}
      </div>

      <div className="min-w-36 px-6 py-4">
        <p className="text-sm/5 font-semibold">{formatCurrency(transaction.account.currency, transaction.amount)}</p>
        {isNonTransfer && transaction.account.currency !== baseCurrency && (
          <p className="text-gray text-xs/5">({formatCurrency(baseCurrency, transaction.baseAmount)})</p>
        )}
        {isTransfer && transaction.amountTo && (
          <p className="text-gray text-sm/5">
            → {formatCurrency(transaction.accountTo!.currency, transaction.amountTo)}
          </p>
        )}
      </div>

      <div className="px-6 py-4">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="icon">
              <MoreHorizontalIcon className="size-4" />
              <span className="sr-only">Open menu</span>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLinkItem to="/transactions/$transactionId" params={{ transactionId: transaction.id }}>
              <ReceiptTextIcon className="mr-1" /> View details
            </DropdownMenuLinkItem>
            <DropdownMenuItem onClick={() => editTransaction(transaction)}>
              <Edit2Icon className="mr-1" />
              Edit transaction
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => deleteTransaction(transaction)} variant="destructive">
              <Trash2Icon className="mr-1" />
              Delete
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </Box>
  );
}
