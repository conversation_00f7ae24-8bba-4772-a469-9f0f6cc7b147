import { useState } from "react";

import { SearchIcon } from "lucide-react";

import { PaginationBlock } from "~/components/blocks";
import { Button } from "~/components/ui/button";
import { Input } from "~/components/ui/input";

import { DEFAULT_PAGE_SIZE } from "../constants";
import { useTransactionActions, useTransactions } from "../hooks";
import { filterTransactionsBySearch } from "../utils";
import TransactionRow from "./transaction-row";

interface Props {
  searchQuery?: string;
  onSearchChange?: (query: string) => void;
}

export default function TransactionsList({ searchQuery = "", onSearchChange }: Props) {
  const [currentPage, setCurrentPage] = useState(1);

  const { createTransaction } = useTransactionActions();
  const { transactions, meta, isLoading, error, totalPages } = useTransactions({
    page: currentPage,
    limit: DEFAULT_PAGE_SIZE,
  });

  // Filter transactions by search query
  const filteredTransactions = filterTransactionsBySearch(transactions, searchQuery);

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onSearchChange?.(e.target.value);
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center py-12">
        <div className="text-center">
          <h3 className="text-lg font-semibold text-gray-900">Error loading transactions</h3>
          <p className="mt-2 text-sm text-gray-600">
            {error.message || "An unexpected error occurred while loading transactions."}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with search and create button */}
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        {/* Search */}
        <div className="relative max-w-md flex-1">
          <SearchIcon className="absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 text-gray-400" />
          <Input
            placeholder="Search transactions..."
            value={searchQuery}
            onChange={handleSearchChange}
            className="pl-10"
            type="search"
          />
        </div>

        {/* Create button */}
        <Button onClick={createTransaction}>Add Transaction</Button>
      </div>

      {/* Loading state */}
      {isLoading && (
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <div className="mx-auto h-8 w-8 animate-spin rounded-full border-4 border-gray-300 border-t-blue-600"></div>
            <p className="mt-2 text-sm text-gray-600">Loading transactions...</p>
          </div>
        </div>
      )}

      {/* Empty state */}
      {!isLoading && filteredTransactions.length === 0 && (
        <div className="flex flex-col items-center justify-center py-12">
          <div className="text-center">
            <h3 className="text-lg font-semibold text-gray-900">
              {searchQuery ? "No transactions found" : "No transactions yet"}
            </h3>
            <p className="mt-2 text-sm text-gray-600">
              {searchQuery
                ? "Try adjusting your search terms or clear the search to see all transactions."
                : "Get started by creating your first transaction."}
            </p>
            {!searchQuery && (
              <Button onClick={createTransaction} className="mt-4">
                Add Your First Transaction
              </Button>
            )}
          </div>
        </div>
      )}

      {/* Transactions list */}
      {!isLoading && filteredTransactions.length > 0 && (
        <>
          <div className="space-y-3">
            {filteredTransactions.map((transaction) => (
              <TransactionRow key={transaction.id} transaction={transaction} />
            ))}
          </div>

          <PaginationBlock
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={handlePageChange}
            className="flex justify-center"
          />
        </>
      )}

      {/* Results summary */}
      {!isLoading && meta && (
        <div className="text-center text-sm text-gray-600">
          {searchQuery ? (
            <p>
              Showing {filteredTransactions.length} of {meta.total} transactions
              {searchQuery && ` matching "${searchQuery}"`}
            </p>
          ) : (
            <p>
              Showing {meta.limit * (meta.page - 1) + 1} to {Math.min(meta.limit * meta.page, meta.total)} of{" "}
              {meta.total} transactions
            </p>
          )}
        </div>
      )}
    </div>
  );
}
