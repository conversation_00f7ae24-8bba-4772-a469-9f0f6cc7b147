import type { Transaction } from "../types";

import { useConfirm } from "~/features/ui/confirmations/hooks";

import { useTransactionActionsStore } from "../store";
import { useCreateTransaction } from "./use-create-transaction";
import { useDeleteTransaction } from "./use-delete-transaction";
import { useUpdateTransaction } from "./use-update-transaction";

/**
 * Main hook that provides all transaction CRUD operations and dialog actions.
 * This is the primary interface for transaction management throughout the app.
 */
export function useTransactionActions() {
  const ask = useConfirm();

  const { openCreateTransactionDialog, openEditTransactionDialog, closeTransactionDialog } =
    useTransactionActionsStore();

  const { createTransaction: createTransactionMutation, isLoading: isCreating } = useCreateTransaction();
  const { updateTransaction: updateTransactionMutation, isLoading: isUpdating } = useUpdateTransaction();
  const { deleteTransaction: deleteTransactionMutation, isLoading: isDeleting } = useDeleteTransaction();

  const createTransaction = () => {
    openCreateTransactionDialog();
  };

  const editTransaction = (transaction: Transaction) => {
    openEditTransactionDialog(transaction);
  };

  const deleteTransaction = async (transaction: Transaction) => {
    const confirmed = await ask({
      title: "Delete transaction",
      description: `Are you sure you want to delete this transaction? This action cannot be undone.`,
      confirmText: "Yes, delete",
      cancelText: "Cancel",
      variant: "destructive",
    });

    if (confirmed) {
      deleteTransactionMutation(transaction);
    }
  };

  return {
    // Dialog actions
    openCreateTransactionDialog,
    openEditTransactionDialog,
    closeTransactionDialog,

    // CRUD operations
    createTransaction,
    editTransaction,
    deleteTransaction,

    // Direct mutation functions (for form submissions)
    createTransactionMutation,
    updateTransactionMutation,
    deleteTransactionMutation,

    // Loading states
    isCreating,
    isUpdating,
    isDeleting,
    isLoading: isCreating || isUpdating || isDeleting,
  };
}
