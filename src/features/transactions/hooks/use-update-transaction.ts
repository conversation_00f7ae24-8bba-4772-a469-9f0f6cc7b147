import type { TransactionUpdateRequest } from "~/api/types.gen";

import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

import {
  getTransactionQueryKey,
  listTransactionsQueryKey,
  updateTransactionMutation,
} from "~/api/@tanstack/react-query.gen";

import useTransactionActionsStore from "../store/transaction-actions-store";

export function useUpdateTransaction() {
  const queryClient = useQueryClient();

  const { closeTransactionDialog } = useTransactionActionsStore();

  const mutation = useMutation({
    ...updateTransactionMutation(),
    onSuccess: (data) => {
      // Invalidate transactions queries to refresh the data
      void queryClient.invalidateQueries({ queryKey: listTransactionsQueryKey() });
      void queryClient.invalidateQueries({ queryKey: getTransactionQueryKey({ path: { id: data.id } }) });

      toast.success("Transaction updated successfully!", {
        description: `Transaction has been updated.`,
      });

      closeTransactionDialog();
    },
    onError: (error) => {
      toast.error("Failed to update transaction", {
        description: error.message || "An unexpected error occurred.",
      });
    },
  });

  const updateTransaction = (transactionId: string, data: TransactionUpdateRequest) => {
    mutation.mutate({
      path: { id: transactionId },
      body: data,
    });
  };

  return { updateTransaction, isLoading: mutation.isPending, error: mutation.error };
}
