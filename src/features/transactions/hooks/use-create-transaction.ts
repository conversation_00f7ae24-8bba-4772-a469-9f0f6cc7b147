import type { TransactionCreateRequest } from "~/api/types.gen";

import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

import { createTransactionMutation, listTransactionsQueryKey } from "~/api/@tanstack/react-query.gen";

import useTransactionActionsStore from "../store/transaction-actions-store";

export function useCreateTransaction() {
  const queryClient = useQueryClient();

  const { closeTransactionDialog } = useTransactionActionsStore();

  const mutation = useMutation({
    ...createTransactionMutation(),
    onSuccess: (data) => {
      // Invalidate transactions query to refresh the list
      void queryClient.invalidateQueries({ queryKey: listTransactionsQueryKey() });

      toast.success("Transaction created successfully!", {
        description: `Transaction for ${data.amount} ${data.account.currency} has been added.`,
      });

      closeTransactionDialog();
    },
    onError: (error) => {
      toast.error("Failed to create transaction", {
        description: error.message || "An unexpected error occurred.",
      });
    },
  });

  const createTransaction = (data: TransactionCreateRequest) => {
    mutation.mutate({ body: data });
  };

  return { createTransaction, isLoading: mutation.isPending, error: mutation.error };
}
