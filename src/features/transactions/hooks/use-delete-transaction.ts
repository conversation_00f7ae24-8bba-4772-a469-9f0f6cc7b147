import type { TransactionResponse } from "~/api/types.gen";

import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

import {
  deleteTransactionMutation,
  getTransactionQueryKey,
  listTransactionsQuery<PERSON>ey,
} from "~/api/@tanstack/react-query.gen";

export function useDeleteTransaction() {
  const queryClient = useQueryClient();

  const mutation = useMutation({
    ...deleteTransactionMutation(),
    onSuccess: (_, variables) => {
      // Invalidate transactions queries to refresh the data
      void queryClient.invalidateQueries({ queryKey: listTransactionsQueryKey() });
      void queryClient.invalidateQueries({ queryKey: getTransactionQueryKey({ path: { id: variables.path.id } }) });

      toast.success("Transaction deleted successfully!", {
        description: "The transaction has been removed from your records.",
      });
    },
    onError: (error) => {
      toast.error("Failed to delete transaction", {
        description: error.message || "An unexpected error occurred.",
      });
    },
  });

  const deleteTransaction = (transaction: TransactionResponse) => {
    mutation.mutate({ path: { id: transaction.id } });
  };

  return { deleteTransaction, isLoading: mutation.isPending, error: mutation.error };
}
