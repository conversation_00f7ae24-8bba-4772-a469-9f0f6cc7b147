import type {
  TransactionCreateRequest,
  TransactionListResponse,
  TransactionResponse,
  TransactionType,
  TransactionUpdateRequest,
} from "~/api/types.gen";

// Re-export API types for convenience
export type {
  TransactionCreateRequest,
  TransactionListResponse,
  TransactionResponse as Transaction,
  TransactionType,
  TransactionUpdateRequest,
};

// Additional types for the transactions feature
export interface TransactionFormData extends Omit<TransactionCreateRequest, "transactionDate"> {
  transactionDate: Date;
}

export interface TransactionUpdateFormData extends Omit<TransactionUpdateRequest, "transactionDate"> {
  transactionDate?: Date;
}

// Transaction display helpers
export interface TransactionDisplayData {
  id: string;
  date: string;
  description: string | null;
  amount: string;
  type: TransactionType;
  accountName: string;
  accountToName?: string;
  categoryName?: string;
  categoryColor?: string;
  categoryIcon?: string;
}
