import { Button } from "~/components/ui/button";
import { useConfirm } from "~/features/ui/confirmations/hooks";

export function ExampleUsage() {
  const ask = useConfirm();

  const handleRemoveAccount = async () => {
    const ok = await ask({
      title: "Remove account",
      description: "Are you sure you want to remove this account?",
      confirmText: "Remove",
      cancelText: "Cancel",
      variant: "destructive",
    });

    if (ok) {
      console.log("Account removed!");
      // Perform the actual removal logic here
    } else {
      console.log("Removal cancelled");
    }
  };

  const handleSimpleConfirm = async () => {
    const ok = await ask({
      title: "Simple confirmation",
      description: "This is a simple confirmation dialog.",
    });

    if (ok) {
      console.log("Confirmed!");
    }
  };

  const handleNoDescription = async () => {
    const ok = await ask({
      title: "Are you sure?",
    });

    if (ok) {
      console.log("Confirmed without description!");
    }
  };

  return (
    <div className="space-y-4 p-4">
      <h2 className="text-lg font-semibold">Confirmation Dialog Examples</h2>

      <div className="space-y-2">
        <Button onClick={handleRemoveAccount} variant="destructive">
          Remove Account (Destructive)
        </Button>

        <Button onClick={handleSimpleConfirm}>Simple Confirmation</Button>

        <Button onClick={handleNoDescription} variant="outline">
          No Description
        </Button>
      </div>
    </div>
  );
}
