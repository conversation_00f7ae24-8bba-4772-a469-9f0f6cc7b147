import type {
  Budget,
  BudgetCreateRequest,
  BudgetUpdateRequest,
  BudgetRecord,
  BudgetPeriod,
  BudgetType,
  BudgetListResponse,
  BudgetHistoryResponse,
} from "~/api/types.gen";

// Re-export API types for convenience
export type {
  Budget,
  BudgetCreateRequest,
  BudgetUpdateRequest,
  BudgetRecord,
  BudgetPeriod,
  BudgetType,
  BudgetListResponse,
  BudgetHistoryResponse,
};

// Additional types for the budgets feature
export interface BudgetProgressStatus {
  percentage: number;
  status: "under" | "warning" | "over"; // under 75%, 75-100%, over 100%
}

export interface BudgetWithProgress extends Budget {
  progress?: BudgetProgressStatus;
}
