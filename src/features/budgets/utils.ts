import type { Budget, BudgetProgressStatus, BudgetWithProgress } from "./types";
import { PROGRESS_THRESHOLDS } from "./constants";

/**
 * Calculate budget progress status based on used and planned amounts
 */
export function calculateBudgetProgress(usedAmount: number, plannedAmount: number): BudgetProgressStatus {
  if (plannedAmount === 0) {
    return {
      percentage: 0,
      status: "under",
    };
  }

  const percentage = Math.round((usedAmount / plannedAmount) * 100);

  let status: BudgetProgressStatus["status"];
  if (percentage >= PROGRESS_THRESHOLDS.OVER) {
    status = "over";
  } else if (percentage >= PROGRESS_THRESHOLDS.WARNING) {
    status = "warning";
  } else {
    status = "under";
  }

  return {
    percentage,
    status,
  };
}

/**
 * Add progress information to a budget
 */
export function addBudgetProgress(budget: Budget): BudgetWithProgress {
  if (!budget.currentRecord) {
    return budget;
  }

  const usedAmount = parseFloat(budget.currentRecord.usedAmount);
  const plannedAmount = parseFloat(budget.currentRecord.plannedAmount);
  const progress = calculateBudgetProgress(usedAmount, plannedAmount);

  return {
    ...budget,
    progress,
  };
}

/**
 * Add progress information to multiple budgets
 */
export function addBudgetsProgress(budgets: Budget[]): BudgetWithProgress[] {
  return budgets.map(addBudgetProgress);
}

/**
 * Filter budgets by archived status
 */
export function filterBudgetsByArchived(budgets: Budget[], includeArchived: boolean): Budget[] {
  if (includeArchived) {
    return budgets;
  }
  return budgets.filter((budget) => !budget.isArchived);
}

/**
 * Format budget period for display
 */
export function formatBudgetPeriod(period: Budget["period"]): string {
  const periodMap = {
    week: "Weekly",
    month: "Monthly",
    quarter: "Quarterly",
    year: "Yearly",
  };
  return periodMap[period];
}

/**
 * Format budget type for display
 */
export function formatBudgetType(type: Budget["type"]): string {
  const typeMap = {
    fixed: "Fixed Amount",
    percentage: "Percentage",
  };
  return typeMap[type];
}

/**
 * Get progress color class based on status
 */
export function getProgressColorClass(status: BudgetProgressStatus["status"]): string {
  const colorMap = {
    under: "bg-green-500",
    warning: "bg-yellow-500",
    over: "bg-red-500",
  };
  return colorMap[status];
}

/**
 * Get progress text color class based on status
 */
export function getProgressTextColorClass(status: BudgetProgressStatus["status"]): string {
  const colorMap = {
    under: "text-green-600",
    warning: "text-yellow-600",
    over: "text-red-600",
  };
  return colorMap[status];
}
