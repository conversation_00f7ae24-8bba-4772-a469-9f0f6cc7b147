import type { Budget } from "~/api/types.gen";
import type { BudgetActionsStore } from "./types";

import { create } from "zustand";
import { devtools } from "zustand/middleware";
import { immer } from "zustand/middleware/immer";

const useBudgetActionsStore = create<BudgetActionsStore>()(
  devtools(
    immer((set) => ({
      // Initial state
      budgetDialog: {
        isOpen: false,
        editingBudget: null,
      },

      // Budget dialog actions
      openCreateBudgetDialog() {
        set(
          (state) => {
            state.budgetDialog.isOpen = true;
            state.budgetDialog.editingBudget = null;
          },
          undefined,
          "budgets/openCreateBudgetDialog"
        );
      },

      openEditBudgetDialog(budget: Budget) {
        set(
          (state) => {
            state.budgetDialog.isOpen = true;
            state.budgetDialog.editingBudget = budget;
          },
          undefined,
          "budgets/openEditBudgetDialog"
        );
      },

      closeBudgetDialog() {
        set(
          (state) => {
            state.budgetDialog.isOpen = false;
            state.budgetDialog.editingBudget = null;
          },
          undefined,
          "budgets/closeBudgetDialog"
        );
      },
    })),
    {
      name: "budgetActionsStore",
    }
  )
);

export default useBudgetActionsStore;
