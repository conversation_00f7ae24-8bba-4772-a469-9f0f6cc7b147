import type { Budget } from "~/api/types.gen";

export interface BudgetDialogState {
  isOpen: boolean;
  editingBudget: Budget | null;
}

export interface BudgetActionsState {
  budgetDialog: BudgetDialogState;
}

export interface BudgetActionsActions {
  // Budget dialog actions
  openCreateBudgetDialog: () => void;
  openEditBudgetDialog: (budget: Budget) => void;
  closeBudgetDialog: () => void;
}

export type BudgetActionsStore = BudgetActionsState & BudgetActionsActions;
