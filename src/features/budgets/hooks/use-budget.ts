import { useMemo } from "react";
import { useQuery } from "@tanstack/react-query";

import { getBudgetOptions } from "~/api/@tanstack/react-query.gen";

import { addBudgetProgress } from "../utils";

export function useBudget(budgetId: string) {
  const {
    data: budgetData,
    isLoading,
    error,
    refetch,
  } = useQuery(getBudgetOptions({ path: { id: budgetId } }));

  const budget = useMemo(() => (budgetData ? addBudgetProgress(budgetData) : undefined), [budgetData]);

  return {
    budget,
    isLoading,
    error,
    refetch,
  };
}
