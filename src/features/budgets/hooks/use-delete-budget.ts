import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

import { deleteBudgetMutation, getBudgetQueryKey, listBudgetsQueryKey } from "~/api/@tanstack/react-query.gen";

export function useDeleteBudget() {
  const queryClient = useQueryClient();

  const mutation = useMutation({
    ...deleteBudgetMutation(),
    onSuccess: (_, variables) => {
      // Invalidate budgets list to refetch
      void queryClient.invalidateQueries({
        queryKey: listBudgetsQueryKey(),
      });

      // Remove specific budget query from cache
      queryClient.removeQueries({
        queryKey: getBudgetQueryKey({ path: { id: variables.path.id } }),
      });

      toast.success("Budget deleted successfully", {
        description: "The budget has been permanently deleted.",
      });
    },
    onError: (error) => {
      toast.error("Failed to delete budget", {
        description: error.message || "An unexpected error occurred.",
      });
    },
  });

  const deleteBudget = (budgetId: string) => {
    mutation.mutate({ path: { id: budgetId } });
  };

  return {
    deleteBudget,
    isLoading: mutation.isPending,
    error: mutation.error,
  };
}
