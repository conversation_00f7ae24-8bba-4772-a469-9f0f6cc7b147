import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

import { updateBudgetMutation, listBudgetsQueryKey, getBudgetQueryKey } from "~/api/@tanstack/react-query.gen";
import type { BudgetUpdateRequest } from "~/api/types.gen";

export function useUpdateBudget() {
  const queryClient = useQueryClient();

  const mutation = useMutation({
    ...updateBudgetMutation(),
    onSuccess: (data, variables) => {
      // Invalidate budgets list to refetch
      queryClient.invalidateQueries({
        queryKey: listBudgetsQueryKey(),
      });

      // Invalidate specific budget query
      queryClient.invalidateQueries({
        queryKey: getBudgetQueryKey({ path: { id: variables.path.id } }),
      });

      toast.success("Budget updated successfully", {
        description: `"${data.name}" has been updated.`,
      });
    },
    onError: (error) => {
      toast.error("Failed to update budget", {
        description: error.message || "An unexpected error occurred.",
      });
    },
  });

  const updateBudget = (budgetId: string, data: BudgetUpdateRequest) => {
    mutation.mutate({ path: { id: budgetId }, body: data });
  };

  return {
    updateBudget,
    isLoading: mutation.isPending,
    error: mutation.error,
  };
}
