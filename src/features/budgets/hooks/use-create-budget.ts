import type { BudgetCreateRequest } from "~/api/types.gen";

import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

import { createBudgetMutation, listBudgetsQueryKey } from "~/api/@tanstack/react-query.gen";

export function useCreateBudget() {
  const queryClient = useQueryClient();

  const mutation = useMutation({
    ...createBudgetMutation(),
    onSuccess: (data) => {
      // Invalidate budgets list to refetch
      void queryClient.invalidateQueries({
        queryKey: listBudgetsQueryKey(),
      });

      toast.success("Budget created successfully", {
        description: `"${data.name}" has been created.`,
      });
    },
    onError: (error) => {
      toast.error("Failed to create budget", {
        description: error.message || "An unexpected error occurred.",
      });
    },
  });

  const createBudget = (data: BudgetCreateRequest) => {
    mutation.mutate({ body: data });
  };

  return {
    createBudget,
    isLoading: mutation.isPending,
    error: mutation.error,
  };
}
