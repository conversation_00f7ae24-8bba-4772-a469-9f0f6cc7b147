import { useQuery } from "@tanstack/react-query";

import { getBudgetHistoryOptions } from "~/api/@tanstack/react-query.gen";

interface UseBudgetHistoryOptions {
  budgetId: string;
  page?: number;
  limit?: number;
}

export function useBudgetHistory({ budgetId, page = 1, limit = 10 }: UseBudgetHistoryOptions) {
  const {
    data: historyData,
    isLoading,
    error,
    refetch,
  } = useQuery(
    getBudgetHistoryOptions({
      path: { id: budgetId },
      query: { page, limit },
    })
  );

  const records = historyData?.items ?? [];
  const meta = historyData?.meta;

  return {
    records,
    meta,
    isLoading,
    error,
    refetch,
  };
}
