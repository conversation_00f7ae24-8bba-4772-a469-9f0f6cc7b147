import { useMemo } from "react";
import { useQuery } from "@tanstack/react-query";

import { listBudgetsOptions } from "~/api/@tanstack/react-query.gen";

import { addBudgetsProgress, filterBudgetsByArchived } from "../utils";

interface UseBudgetsOptions {
  includeArchived?: boolean;
}

export function useBudgets(options: UseBudgetsOptions = {}) {
  const { includeArchived = false } = options;

  const {
    data: budgetsData,
    isLoading,
    error,
    refetch,
  } = useQuery(
    listBudgetsOptions({
      query: { includeArchived },
    })
  );

  const budgets = useMemo(() => budgetsData ?? [], [budgetsData]);
  const budgetsWithProgress = useMemo(() => addBudgetsProgress(budgets), [budgets]);
  const activeBudgets = useMemo(() => filterBudgetsByArchived(budgetsWithProgress, false), [budgetsWithProgress]);
  const archivedBudgets = useMemo(
    () => budgetsWithProgress.filter((budget) => budget.isArchived),
    [budgetsWithProgress]
  );

  return {
    budgets: budgetsWithProgress,
    activeBudgets,
    archivedBudgets,
    isLoading,
    error,
    refetch,
  };
}
