import type { BudgetPeriod, BudgetType } from "./types";

// Budget period options for forms
export const budgetPeriodOptions = [
  { value: "week" as BudgetPeriod, label: "Week" },
  { value: "month" as BudgetPeriod, label: "Month" },
  { value: "quarter" as BudgetPeriod, label: "Quarter" },
  { value: "year" as BudgetPeriod, label: "Year" },
] as const;

// Budget type options for forms
export const budgetTypeOptions = [
  { value: "fixed" as BudgetType, label: "Fixed Amount" },
  { value: "percentage" as BudgetType, label: "Percentage" },
] as const;

// Default budget period
export const defaultBudgetPeriod: BudgetPeriod = "month";

// Default budget type
export const defaultBudgetType: BudgetType = "fixed";

// Progress status thresholds
export const PROGRESS_THRESHOLDS = {
  WARNING: 75, // 75% usage shows warning (yellow)
  OVER: 100, // 100% usage shows over (red)
} as const;

// Period icons for UI
export const periodIcons = {
  week: "📅",
  month: "🗓️",
  quarter: "📊",
  year: "🗓️",
} as const;

// Type icons for UI
export const typeIcons = {
  fixed: "💰",
  percentage: "📊",
} as const;
