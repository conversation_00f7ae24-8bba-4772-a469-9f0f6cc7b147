import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>Header, DialogTitle } from "~/components/ui/dialog";

import { useBudgetActionsStore } from "../store";
import BudgetFormCreate from "./budget-form-create";
import BudgetFormUpdate from "./budget-form-update";

export default function BudgetDialog() {
  const { budgetDialog, closeBudgetDialog } = useBudgetActionsStore();
  const { isOpen, editingBudget } = budgetDialog;

  const isEditing = !!editingBudget;

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && closeBudgetDialog()}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>{isEditing ? "Edit Budget" : "Create Budget"}</DialogTitle>
        </DialogHeader>

        {isEditing && editingBudget ? (
          <BudgetFormUpdate budget={editingBudget} />
        ) : (
          <BudgetFormCreate />
        )}
      </DialogContent>
    </Dialog>
  );
}
