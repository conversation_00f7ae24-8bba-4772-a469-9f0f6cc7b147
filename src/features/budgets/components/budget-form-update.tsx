import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod";
import { useEffect } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

import { zBudgetUpdateRequest } from "~/api/zod.gen";
import { InputMultiSelect, InputRadioGroup, InputSwitch, InputText, InputTextarea } from "~/components/inputs";
import { Button } from "~/components/ui/button";
import { Form } from "~/components/ui/form";
import { useAccounts } from "~/features/accounts/hooks";

import { budgetTypeOptions } from "../constants";
import { useBudgetActions } from "../hooks";
import type { Budget } from "../types";

type FormData = z.infer<typeof zBudgetUpdateRequest>;

interface Props {
  budget: Budget;
  onSuccess?: () => void;
}

export default function BudgetFormUpdate({ budget, onSuccess }: Props) {
  const { updateBudgetMutation, closeBudgetDialog } = useBudgetActions();
  const { accounts } = useAccounts();

  const form = useForm<FormData>({
    resolver: zodResolver(zBudgetUpdateRequest),
    defaultValues: {
      name: budget.name,
      description: budget.description,
      type: budget.type,
      value: budget.value,
      accounts: budget.accounts,
      isArchived: budget.isArchived,
    },
  });

  // Update form when budget changes
  useEffect(() => {
    form.reset({
      name: budget.name,
      description: budget.description,
      type: budget.type,
      value: budget.value,
      accounts: budget.accounts,
      isArchived: budget.isArchived,
    });
  }, [budget, form]);

  const watchedType = form.watch("type");

  const handleSubmit = (data: FormData) => {
    updateBudgetMutation(budget.id, data);
    onSuccess?.();
    closeBudgetDialog();
  };

  const accountOptions = accounts.map((account) => ({
    value: account.id,
    label: `${account.name} (${account.currency})`,
  }));

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
        <InputText
          control={form.control}
          name="name"
          label="Budget Name"
          placeholder="Enter budget name"
          description="A descriptive name for your budget"
        />

        <InputTextarea
          control={form.control}
          name="description"
          label="Description"
          hint="Optional"
          placeholder="Enter budget description"
          description="Additional details about this budget"
          className="resize-none"
        />

        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <InputRadioGroup
            control={form.control}
            name="type"
            label="Budget Type"
            options={budgetTypeOptions}
            description="Whether this is a fixed amount or percentage"
          />

          <InputText
            control={form.control}
            name="value"
            label={watchedType === "percentage" ? "Percentage Value" : "Budget Amount"}
            type="number"
            step={watchedType === "percentage" ? "0.1" : "0.01"}
            placeholder={watchedType === "percentage" ? "0.0" : "0.00"}
            description={
              watchedType === "percentage"
                ? "Percentage of total income/balance to budget"
                : "Fixed amount to budget for this period"
            }
          />
        </div>

        <InputMultiSelect
          control={form.control}
          name="accounts"
          label="Account Restrictions"
          hint="Optional"
          options={accountOptions}
          placeholder="Select accounts (optional)"
          description="Restrict this budget to specific accounts. Leave empty to include all accounts."
        />

        <InputSwitch
          control={form.control}
          name="isArchived"
          label="Archive Budget"
          description="Archived budgets are hidden from the main view but can still be accessed"
        />

        <div className="flex justify-end gap-3">
          <Button type="button" variant="outline" onClick={closeBudgetDialog}>
            Cancel
          </Button>
          <Button type="submit">Update Budget</Button>
        </div>
      </form>
    </Form>
  );
}
