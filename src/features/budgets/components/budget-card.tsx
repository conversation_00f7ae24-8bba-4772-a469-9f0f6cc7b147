import type { BudgetWithProgress } from "../types";

import { Link } from "@tanstack/react-router";
import { Archive, Edit, MoreHorizontal, Trash2 } from "lucide-react";

import { Badge } from "~/components/ui/badge";
import { Button } from "~/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "~/components/ui/dropdown-menu";
import { useBaseCurrency } from "~/features/auth/hooks";
import { formatCurrency } from "~/lib/formatters";

import { periodIcons, typeIcons } from "../constants";
import { useBudgetActions } from "../hooks";
import { formatBudgetPeriod, formatBudgetType, getProgressTextColorClass } from "../utils";
import BudgetProgressBar from "./budget-progress-bar";

interface Props {
  budget: BudgetWithProgress;
}

export default function BudgetCard({ budget }: Props) {
  const baseCurrency = useBaseCurrency();
  const { editBudget, deleteBudget, archiveBudget } = useBudgetActions();

  const currentRecord = budget.currentRecord;
  const progress = budget.progress;

  const handleEdit = () => {
    editBudget(budget);
  };

  const handleDelete = () => {
    void deleteBudget(budget);
  };

  const handleArchive = () => {
    void archiveBudget(budget);
  };

  return (
    <Card className="transition-shadow hover:shadow-md">
      <CardHeader className="flex flex-row items-start justify-between space-y-0 pb-2">
        <div className="space-y-1">
          <CardTitle className="text-base">
            <Link to="/budgets/$budgetId" params={{ budgetId: budget.id }} className="hover:underline">
              {budget.name}
            </Link>
          </CardTitle>
          {budget.description && <p className="text-muted-foreground text-sm">{budget.description}</p>}
        </div>

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={handleEdit}>
              <Edit className="mr-2 h-4 w-4" />
              Edit
            </DropdownMenuItem>
            <DropdownMenuItem onClick={handleArchive}>
              <Archive className="mr-2 h-4 w-4" />
              {budget.isArchived ? "Unarchive" : "Archive"}
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={handleDelete} className="text-destructive">
              <Trash2 className="mr-2 h-4 w-4" />
              Delete
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Budget info badges */}
        <div className="flex flex-wrap gap-2">
          <Badge variant="secondary" className="text-xs">
            {periodIcons[budget.period]} {formatBudgetPeriod(budget.period)}
          </Badge>
          <Badge variant="outline" className="text-xs">
            {typeIcons[budget.type]} {formatBudgetType(budget.type)}
          </Badge>
          {budget.isArchived && (
            <Badge variant="destructive" className="text-xs">
              Archived
            </Badge>
          )}
        </div>

        {/* Account restrictions */}
        {budget.accounts && budget.accounts.length > 0 && (
          <div className="text-muted-foreground text-sm">
            <span className="font-medium">Restricted to:</span> {budget.accounts.length} account(s)
          </div>
        )}

        {/* Current period progress */}
        {currentRecord && progress ? (
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span className="text-muted-foreground">Current Period</span>
              <span className={getProgressTextColorClass(progress.status)}>{progress.percentage}%</span>
            </div>

            <BudgetProgressBar progress={progress} />

            <div className="flex items-center justify-between text-sm">
              <span className="text-muted-foreground">
                Used: {formatCurrency(parseFloat(currentRecord.usedAmount), baseCurrency)}
              </span>
              <span className="text-muted-foreground">
                Budget: {formatCurrency(parseFloat(currentRecord.plannedAmount), baseCurrency)}
              </span>
            </div>

            <div className="text-muted-foreground text-xs">
              Period: {new Date(currentRecord.startDate).toLocaleDateString()} -{" "}
              {new Date(currentRecord.endDate).toLocaleDateString()}
            </div>
          </div>
        ) : (
          <div className="text-muted-foreground text-sm">No current period data available</div>
        )}
      </CardContent>
    </Card>
  );
}
