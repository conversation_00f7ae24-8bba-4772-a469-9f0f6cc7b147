import { useState } from "react";
import { PlusIcon } from "lucide-react";

import { LoadingIndicator } from "~/components/elements";
import { Button } from "~/components/ui/button";
import { Switch } from "~/components/ui/switch";
import { Label } from "~/components/ui/label";

import type { BudgetWithProgress } from "../types";
import { useBudgetActions } from "../hooks";
import BudgetCard from "./budget-card";

interface Props {
  budgets: BudgetWithProgress[];
  activeBudgets: BudgetWithProgress[];
  archivedBudgets: BudgetWithProgress[];
  isLoading?: boolean;
}

export default function BudgetsList(props: Props) {
  const { budgets, activeBudgets, archivedBudgets, isLoading = false } = props;
  const { createBudget } = useBudgetActions();

  const [showArchived, setShowArchived] = useState(false);

  const displayedBudgets = showArchived ? budgets : activeBudgets;

  if (isLoading) {
    return <LoadingIndicator />;
  }

  return (
    <div className="space-y-6">
      {/* <PERSON>er controls */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div className="flex items-center space-x-2">
          <Switch
            id="show-archived"
            checked={showArchived}
            onCheckedChange={setShowArchived}
          />
          <Label htmlFor="show-archived">Show archived budgets</Label>
          {showArchived && archivedBudgets.length > 0 && (
            <span className="text-sm text-muted-foreground">
              ({archivedBudgets.length} archived)
            </span>
          )}
        </div>

        <Button onClick={createBudget}>
          <PlusIcon className="mr-2 h-4 w-4" />
          Add Budget
        </Button>
      </div>

      {/* Budgets grid */}
      {displayedBudgets.length === 0 ? (
        <div className="py-12 text-center">
          <p className="text-muted-foreground mb-4">
            {showArchived
              ? "No budgets found"
              : activeBudgets.length === 0 && archivedBudgets.length > 0
              ? "No active budgets. Toggle to show archived budgets."
              : "No budgets found"}
          </p>
          {!showArchived && activeBudgets.length === 0 && (
            <Button onClick={createBudget}>
              <PlusIcon className="mr-2 h-4 w-4" />
              Create your first budget
            </Button>
          )}
        </div>
      ) : (
        <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
          {displayedBudgets.map((budget) => (
            <BudgetCard key={budget.id} budget={budget} />
          ))}
        </div>
      )}

      {/* Summary info */}
      {!showArchived && activeBudgets.length > 0 && archivedBudgets.length > 0 && (
        <div className="text-center text-sm text-muted-foreground">
          Showing {activeBudgets.length} active budget{activeBudgets.length !== 1 ? "s" : ""}.{" "}
          <button
            onClick={() => setShowArchived(true)}
            className="text-primary hover:underline"
          >
            Show {archivedBudgets.length} archived budget{archivedBudgets.length !== 1 ? "s" : ""}
          </button>
        </div>
      )}
    </div>
  );
}
