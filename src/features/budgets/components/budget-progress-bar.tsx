import { cn } from "~/lib/utils";

import type { BudgetProgressStatus } from "../types";
import { getProgressColorClass } from "../utils";

interface Props {
  progress: BudgetProgressStatus;
  className?: string;
}

export default function BudgetProgressBar({ progress, className }: Props) {
  const { percentage, status } = progress;
  const colorClass = getProgressColorClass(status);

  return (
    <div className={cn("w-full bg-gray-200 rounded-full h-2", className)}>
      <div
        className={cn("h-2 rounded-full transition-all duration-300", colorClass)}
        style={{ width: `${Math.min(percentage, 100)}%` }}
      />
    </div>
  );
}
