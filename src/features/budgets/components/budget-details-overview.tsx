import type { BudgetWithProgress } from "../types";

import { Archive, Edit, Trash2 } from "lucide-react";

import { Badge } from "~/components/ui/badge";
import { But<PERSON> } from "~/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { useBaseCurrency } from "~/features/auth/hooks";
import { formatCurrency } from "~/lib/formatters";

import { periodIcons, typeIcons } from "../constants";
import { useBudgetActions } from "../hooks";
import { formatBudgetPeriod, formatBudgetType, getProgressTextColorClass } from "../utils";
import BudgetProgressBar from "./budget-progress-bar";

interface Props {
  budget: BudgetWithProgress;
}

export default function BudgetDetailsOverview({ budget }: Props) {
  const baseCurrency = useBaseCurrency();
  const { editBudget, deleteBudget, archiveBudget } = useBudgetActions();

  const currentRecord = budget.currentRecord;
  const progress = budget.progress;

  const handleEdit = () => {
    editBudget(budget);
  };

  const handleDelete = () => {
    void deleteBudget(budget);
  };

  const handleArchive = () => {
    void archiveBudget(budget);
  };

  return (
    <div className="space-y-6">
      {/* Budget Header */}
      <Card>
        <CardHeader>
          <div className="flex flex-col items-start justify-between gap-4 sm:flex-row">
            <div className="space-y-2">
              <CardTitle className="text-2xl">{budget.name}</CardTitle>
              {budget.description && <p className="text-muted-foreground">{budget.description}</p>}

              {/* Budget info badges */}
              <div className="flex flex-wrap gap-2">
                <Badge variant="secondary">
                  {periodIcons[budget.period]} {formatBudgetPeriod(budget.period)}
                </Badge>
                <Badge variant="outline">
                  {typeIcons[budget.type]} {formatBudgetType(budget.type)}
                </Badge>
                {budget.isArchived && <Badge variant="destructive">Archived</Badge>}
              </div>
            </div>

            <div className="flex gap-2">
              <Button variant="outline" size="sm" onClick={handleEdit}>
                <Edit className="mr-2 h-4 w-4" />
                Edit
              </Button>
              <Button variant="outline" size="sm" onClick={handleArchive}>
                <Archive className="mr-2 h-4 w-4" />
                {budget.isArchived ? "Unarchive" : "Archive"}
              </Button>
              <Button variant="destructive" size="sm" onClick={handleDelete}>
                <Trash2 className="mr-2 h-4 w-4" />
                Delete
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Budget Configuration */}
      <Card>
        <CardHeader>
          <CardTitle>Budget Configuration</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            <div>
              <h4 className="text-muted-foreground text-sm font-medium">Budget Value</h4>
              <p className="text-lg">
                {budget.type === "percentage"
                  ? `${budget.value}%`
                  : formatCurrency(parseFloat(budget.value), baseCurrency)}
              </p>
            </div>

            <div>
              <h4 className="text-muted-foreground text-sm font-medium">Period</h4>
              <p className="text-lg">{formatBudgetPeriod(budget.period)}</p>
            </div>
          </div>

          {/* Account restrictions */}
          {budget.accounts && budget.accounts.length > 0 && (
            <div>
              <h4 className="text-muted-foreground mb-2 text-sm font-medium">Account Restrictions</h4>
              <p className="text-sm">
                This budget is restricted to {budget.accounts.length} specific account
                {budget.accounts.length !== 1 ? "s" : ""}.
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Current Period Progress */}
      {currentRecord && progress ? (
        <Card>
          <CardHeader>
            <CardTitle>Current Period Progress</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-lg font-medium">
                {formatCurrency(parseFloat(currentRecord.usedAmount), baseCurrency)} used
              </span>
              <span className={`text-lg font-medium ${getProgressTextColorClass(progress.status)}`}>
                {progress.percentage}%
              </span>
            </div>

            <BudgetProgressBar progress={progress} className="h-3" />

            <div className="grid grid-cols-1 gap-4 text-sm md:grid-cols-3">
              <div>
                <h4 className="text-muted-foreground font-medium">Used Amount</h4>
                <p className="text-lg">{formatCurrency(parseFloat(currentRecord.usedAmount), baseCurrency)}</p>
              </div>

              <div>
                <h4 className="text-muted-foreground font-medium">Budget Amount</h4>
                <p className="text-lg">{formatCurrency(parseFloat(currentRecord.plannedAmount), baseCurrency)}</p>
              </div>

              <div>
                <h4 className="text-muted-foreground font-medium">Remaining</h4>
                <p className="text-lg">
                  {formatCurrency(
                    parseFloat(currentRecord.plannedAmount) - parseFloat(currentRecord.usedAmount),
                    baseCurrency
                  )}
                </p>
              </div>
            </div>

            <div className="text-muted-foreground text-sm">
              <strong>Period:</strong> {new Date(currentRecord.startDate).toLocaleDateString()} -{" "}
              {new Date(currentRecord.endDate).toLocaleDateString()}
            </div>
          </CardContent>
        </Card>
      ) : (
        <Card>
          <CardHeader>
            <CardTitle>Current Period Progress</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground">No current period data available for this budget.</p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
