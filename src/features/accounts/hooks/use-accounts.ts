import { useMemo } from "react";

import { useQuery } from "@tanstack/react-query";

import { listAccountsOptions } from "~/api/@tanstack/react-query.gen";

/**
 * Hook to fetch and manage accounts list.
 * Provides a simple interface for getting all accounts.
 */
export function useAccounts() {
  const { data: accountsData, isLoading, error, refetch } = useQuery(listAccountsOptions());

  const accounts = useMemo(() => accountsData?.filter((account) => account.isActive) ?? [], [accountsData]);

  return { accounts, isLoading, error, refetch };
}
