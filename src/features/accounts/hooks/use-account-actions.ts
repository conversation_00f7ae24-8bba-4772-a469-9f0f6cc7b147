import type { Account } from "~/api/types.gen";

import { useConfirm } from "~/features/ui/confirmations/hooks";

import { useAccountActionsStore } from "../store";
import { useCreateAccount } from "./use-create-account";
import { useDeleteAccount } from "./use-delete-account";
import { useUpdateAccount } from "./use-update-account";

export function useAccountActions() {
  const ask = useConfirm();

  // Store actions
  const { openCreateAccountDialog, openEditAccountDialog, closeAccountDialog } = useAccountActionsStore();

  // Mutation hooks
  const { createAccount: createAccountMutation } = useCreateAccount();
  const { updateAccount: updateAccountMutation } = useUpdateAccount();
  const { deleteAccount: deleteAccountMutation } = useDeleteAccount();

  // Account actions
  const createAccount = () => {
    openCreateAccountDialog();
  };

  const createAccountInGroup = (groupId: string) => {
    openCreateAccountDialog(groupId);
  };

  const editAccount = (account: Account) => {
    openEditAccountDialog(account);
  };

  const deleteAccount = async (account: Account) => {
    const confirmed = await ask({
      title: "Delete account",
      description: `Are you sure you want to delete "${account.name}"? This action cannot be undone.`,
      confirmText: "Yes, delete",
      cancelText: "Cancel",
      variant: "destructive",
    });

    if (confirmed) {
      deleteAccountMutation(account.id);
    }
  };

  return {
    // Dialog actions
    createAccount,
    createAccountInGroup,
    editAccount,
    deleteAccount,
    closeAccountDialog,

    // Direct mutation functions (for form submissions)
    createAccountMutation,
    updateAccountMutation,
    deleteAccountMutation,
  };
}
