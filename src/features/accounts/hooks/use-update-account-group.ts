import type { AccountGroupUpdateRequest } from "~/api/types.gen";

import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

import {
  getAccountGroupQuery<PERSON>ey,
  listAccountGroupsQueryKey,
  listAccountsQueryKey,
  updateAccountGroupMutation,
} from "~/api/@tanstack/react-query.gen";

import { useAccountActionsStore } from "../store";

export function useUpdateAccountGroup() {
  const queryClient = useQueryClient();

  const { closeAccountGroupDialog } = useAccountActionsStore();

  const mutation = useMutation({
    ...updateAccountGroupMutation(),
    onSuccess: (data, variables) => {
      // Invalidate account groups query to refresh the list
      void queryClient.invalidateQueries({ queryKey: listAccountGroupsQueryKey() });

      // Invalidate accounts query since group info might be included
      void queryClient.invalidateQueries({ queryKey: listAccountsQueryKey() });

      // Invalidate the specific account group query
      void queryClient.invalidateQueries({ queryKey: getAccountGroupQueryKey({ path: { id: variables.path.id } }) });

      toast.success("Account group updated successfully!", {
        description: `${data.name} has been updated.`,
      });

      closeAccountGroupDialog();
    },
    onError: (error) => {
      toast.error("Failed to update account group", {
        description: error.message || "An unexpected error occurred.",
      });
    },
  });

  const updateAccountGroup = (groupId: string, data: AccountGroupUpdateRequest) => {
    mutation.mutate({
      path: { id: groupId },
      body: {
        name: data.name,
        color: data.color || undefined,
        iconUrl: data.iconUrl || undefined,
      },
    });
  };

  return {
    updateAccountGroup,
    isLoading: mutation.isPending,
    error: mutation.error,
  };
}
