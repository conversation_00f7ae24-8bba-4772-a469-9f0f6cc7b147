import type { AccountGroup } from "~/api/types.gen";

import { useAccountActionsStore } from "../store";
import { useConfirm } from "~/features/ui/confirmations/hooks";
import { useCreateAccountGroup } from "./use-create-account-group";
import { useUpdateAccountGroup } from "./use-update-account-group";
import { useDeleteAccountGroup } from "./use-delete-account-group";

export function useAccountGroupActions() {
  const ask = useConfirm();
  
  // Store actions
  const {
    openCreateAccountGroupDialog,
    openEditAccountGroupDialog,
    closeAccountGroupDialog,
    addAccountToGroup: openAddAccountToGroupDialog,
  } = useAccountActionsStore();

  // Mutation hooks
  const { createAccountGroup: createAccountGroupMutation } = useCreateAccountGroup();
  const { updateAccountGroup: updateAccountGroupMutation } = useUpdateAccountGroup();
  const { deleteAccountGroup: deleteAccountGroupMutation } = useDeleteAccountGroup();

  // Account group actions
  const createAccountGroup = () => {
    openCreateAccountGroupDialog();
  };

  const editAccountGroup = (accountGroup: AccountGroup) => {
    openEditAccountGroupDialog(accountGroup);
  };

  const deleteAccountGroup = async (accountGroup: AccountGroup) => {
    const confirmed = await ask({
      title: "Delete account group",
      description: `Are you sure you want to delete "${accountGroup.name}"? Accounts in this group will be ungrouped.`,
      confirmText: "Yes, delete",
      cancelText: "Cancel",
      variant: "destructive",
    });

    if (confirmed) {
      deleteAccountGroupMutation(accountGroup.id);
    }
  };

  const addAccountToGroup = (accountGroup: AccountGroup) => {
    openAddAccountToGroupDialog(accountGroup);
  };

  return {
    // Dialog actions
    createAccountGroup,
    editAccountGroup,
    deleteAccountGroup,
    addAccountToGroup,
    closeAccountGroupDialog,
    
    // Direct mutation functions (for form submissions)
    createAccountGroupMutation,
    updateAccountGroupMutation,
    deleteAccountGroupMutation,
  };
}
