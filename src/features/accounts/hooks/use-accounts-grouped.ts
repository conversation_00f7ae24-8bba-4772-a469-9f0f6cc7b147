import { useMemo } from "react";

import { useQuery } from "@tanstack/react-query";

import { listAccountGroupsOptions, listAccountsOptions } from "~/api/@tanstack/react-query.gen";

import { groupAccountsByGroup } from "../utils";

export function useAccountsGrouped() {
  const {
    data: accountsData,
    isLoading: isAccountsLoading,
    error: accountsError,
    refetch: refetchAccounts,
  } = useQuery(listAccountsOptions());
  const {
    data: accountGroupsData,
    isLoading: isAccountGroupsLoading,
    error: accountGroupsError,
    refetch: refetchAccountGroups,
  } = useQuery(listAccountGroupsOptions());

  const accounts = useMemo(() => accountsData ?? [], [accountsData]);
  const accountGroups = useMemo(() => accountGroupsData ?? [], [accountGroupsData]);
  const groupedData = useMemo(() => groupAccountsByGroup(accounts, accountGroups), [accounts, accountGroups]);

  return {
    accounts,
    accountGroups,
    groupedData,
    isLoading: isAccountsLoading || isAccountGroupsLoading,
    error: accountsError || accountGroupsError,
    refetch: () => {
      void refetchAccounts();
      void refetchAccountGroups();
    },
  };
}
