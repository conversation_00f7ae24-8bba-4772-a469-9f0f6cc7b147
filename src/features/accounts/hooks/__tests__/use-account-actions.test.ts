import { renderHook } from "@testing-library/react";
import { describe, expect, it, vi } from "vitest";

import { useAccountActions } from "../use-account-actions";

// Mock the dependencies
vi.mock("../use-create-account", () => ({
  useCreateAccount: () => ({
    createAccount: vi.fn(),
  }),
}));

vi.mock("../use-update-account", () => ({
  useUpdateAccount: () => ({
    updateAccount: vi.fn(),
  }),
}));

vi.mock("../use-delete-account", () => ({
  useDeleteAccount: () => ({
    deleteAccount: vi.fn(),
  }),
}));

vi.mock("~/features/ui/confirmations/hooks", () => ({
  useConfirm: () => vi.fn(),
}));

vi.mock("../store", () => ({
  useAccountActionsStore: () => ({
    openCreateAccountDialog: vi.fn(),
    openEditAccountDialog: vi.fn(),
    closeAccountDialog: vi.fn(),
  }),
}));

describe("useAccountActions", () => {
  it("should return account action functions", () => {
    const { result } = renderHook(() => useAccountActions());

    expect(result.current).toHaveProperty("createAccount");
    expect(result.current).toHaveProperty("editAccount");
    expect(result.current).toHaveProperty("deleteAccount");
    expect(result.current).toHaveProperty("closeAccountDialog");
    expect(result.current).toHaveProperty("createAccountMutation");
    expect(result.current).toHaveProperty("updateAccountMutation");
    expect(result.current).toHaveProperty("deleteAccountMutation");
  });

  it("should provide functions that are callable", () => {
    const { result } = renderHook(() => useAccountActions());

    expect(typeof result.current.createAccount).toBe("function");
    expect(typeof result.current.editAccount).toBe("function");
    expect(typeof result.current.deleteAccount).toBe("function");
  });
});
