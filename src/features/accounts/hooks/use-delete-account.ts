import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useNavigate } from "@tanstack/react-router";
import { toast } from "sonner";

import {
  deleteAccountMutation,
  listAccountGroupsQueryKey,
  listAccountsQueryKey,
} from "~/api/@tanstack/react-query.gen";

export function useDeleteAccount() {
  const queryClient = useQueryClient();
  const navigate = useNavigate();

  const mutation = useMutation({
    ...deleteAccountMutation(),
    onSuccess: () => {
      // Invalidate accounts and account groups queries to refresh the lists
      void queryClient.invalidateQueries({ queryKey: listAccountsQueryKey() });
      void queryClient.invalidateQueries({ queryKey: listAccountGroupsQueryKey() });

      toast.success("Account deleted successfully!", {
        description: "The account has been permanently removed.",
      });

      return navigate({ to: "/accounts" });
    },
    onError: (error) => {
      toast.error("Failed to delete account", {
        description: error.message || "An unexpected error occurred.",
      });
    },
  });

  const deleteAccount = (accountId: string) => {
    mutation.mutate({
      path: { id: accountId },
    });
  };

  return {
    deleteAccount,
    isLoading: mutation.isPending,
    error: mutation.error,
  };
}
