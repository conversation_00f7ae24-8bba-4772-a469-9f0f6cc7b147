import type { AccountUpdateRequest } from "~/api/types.gen";

import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

import {
  getAccountQuery<PERSON>ey,
  listAccountGroupsQuery<PERSON>ey,
  listAccountsQuery<PERSON>ey,
  updateAccountMutation,
} from "~/api/@tanstack/react-query.gen";

import { useAccountActionsStore } from "../store";

export function useUpdateAccount() {
  const queryClient = useQueryClient();

  const { closeAccountDialog } = useAccountActionsStore();

  const mutation = useMutation({
    ...updateAccountMutation(),
    onSuccess: (data, variables) => {
      // Invalidate accounts and account groups queries to refresh the lists
      void queryClient.invalidateQueries({ queryKey: listAccountsQueryKey() });
      void queryClient.invalidateQueries({ queryKey: listAccountGroupsQueryKey() });

      // Invalidate the specific account query
      void queryClient.invalidateQueries({ queryKey: getAccountQueryKey({ path: { id: variables.path.id } }) });

      toast.success("Account updated successfully!", {
        description: `${data.name} has been updated.`,
      });

      closeAccountDialog();
    },
    onError: (error) => {
      toast.error("Failed to update account", {
        description: error.message || "An unexpected error occurred.",
      });
    },
  });

  const updateAccount = (accountId: string, data: AccountUpdateRequest) => {
    mutation.mutate({
      path: { id: accountId },
      body: {
        name: data.name,
        type: data.type,
        currency: data.currency,
        groupId: data.groupId || null,
        description: data.description || undefined,
        color: data.color || undefined,
        overdraftLimit: data.overdraftLimit || undefined,
        isActive: data.isActive,
      },
    });
  };

  return {
    updateAccount,
    isLoading: mutation.isPending,
    error: mutation.error,
  };
}
