import type { Account, AccountGroup } from "~/api/types.gen";
import type { AccountsGroupedData } from "../types";

/**
 * Groups accounts by their account groups
 */
export function groupAccountsByGroup(
  accounts: Account[],
  accountGroups: AccountGroup[]
): AccountsGroupedData {
  const grouped: AccountsGroupedData["grouped"] = [];
  const ungrouped: Account[] = [];

  // Create a map of group ID to group for efficient lookup
  const groupMap = new Map(accountGroups.map(group => [group.id, group]));

  // Separate accounts into grouped and ungrouped
  accounts.forEach(account => {
    if (account.groupId && groupMap.has(account.groupId)) {
      // Find existing group in grouped array or create new entry
      let groupEntry = grouped.find(entry => entry.group.id === account.groupId);
      
      if (!groupEntry) {
        groupEntry = {
          group: groupMap.get(account.groupId)!,
          accounts: []
        };
        grouped.push(groupEntry);
      }
      
      groupEntry.accounts.push(account);
    } else {
      ungrouped.push(account);
    }
  });

  // Sort groups by name
  grouped.sort((a, b) => a.group.name.localeCompare(b.group.name));

  // Sort accounts within each group by name
  grouped.forEach(entry => {
    entry.accounts.sort((a, b) => a.name.localeCompare(b.name));
  });

  // Sort ungrouped accounts by name
  ungrouped.sort((a, b) => a.name.localeCompare(b.name));

  return { grouped, ungrouped };
}
