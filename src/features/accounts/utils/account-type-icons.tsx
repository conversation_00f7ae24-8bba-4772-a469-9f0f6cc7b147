import { 
  Wallet, 
  CreditCard, 
  Building2, 
  PiggyBank, 
  TrendingDown, 
  CircleDot,
  type LucideIcon 
} from "lucide-react";

import type { AccountType } from "~/api/types.gen";

/**
 * Maps account types to their corresponding Lucide React icons
 */
export const ACCOUNT_TYPE_ICONS: Record<AccountType, LucideIcon> = {
  cash: Wallet,
  card: CreditCard,
  bank_account: Building2,
  savings: PiggyBank,
  loan: TrendingDown,
  other: CircleDot,
};

/**
 * Gets the appropriate icon component for an account type
 */
export function getAccountTypeIcon(type: AccountType): LucideIcon {
  return ACCOUNT_TYPE_ICONS[type];
}

/**
 * Gets a human-readable label for an account type
 */
export function getAccountTypeLabel(type: AccountType): string {
  const labels: Record<AccountType, string> = {
    cash: "Cash",
    card: "Card",
    bank_account: "Bank Account",
    savings: "Savings",
    loan: "Loan",
    other: "Other",
  };
  
  return labels[type];
}
