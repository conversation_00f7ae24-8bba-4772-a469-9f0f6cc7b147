import type { Account, AccountGroup, AccountType } from "~/api/types.gen";

// Re-export API types for convenience
export type { Account, AccountGroup, AccountType };

// Additional types for the accounts feature
export interface AccountWithGroup extends Account {
  group?: AccountGroup;
}

export interface AccountsGroupedData {
  grouped: Array<{
    group: AccountGroup;
    accounts: Account[];
  }>;
  ungrouped: Account[];
}
