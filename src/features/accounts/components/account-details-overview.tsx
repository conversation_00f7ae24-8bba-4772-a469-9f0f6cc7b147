import type { Account } from "~/api/types.gen";

import { ArchiveIcon, CheckCircleIcon as Check<PERSON><PERSON>, Edit2Icon, MoreHorizontalIcon, Trash2Icon } from "lucide-react";

import { Box, DefinitionBlock } from "~/components/blocks";
import { Badge } from "~/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "~/components/ui/dropdown-menu";
import { useBaseCurrency } from "~/features/auth/hooks";
import { formatCurrency, formatDate } from "~/lib/formatters";

import { useAccountActions } from "../hooks";
import { getAccountTypeIcon, getAccountTypeLabel } from "../utils";

interface Props {
  account: Account;
}

export default function AccountDetailsOverview({ account }: Props) {
  const { editAccount, deleteAccount } = useAccountActions();

  const baseCurrency = useBaseCurrency();

  const color = account.color || account.group?.color || "#ffffff";
  const Icon = getAccountTypeIcon(account.type);

  return (
    <Box className="flex flex-col gap-6 border-t-[6px] ps-8 pe-4 pt-4 pb-8" style={{ borderColor: color }}>
      <div className="flex items-center gap-4">
        <Icon className="text-gray size-4" />
        <p className="text-foreground text-xs/5 font-semibold uppercase" title="Created at">
          {formatDate(account.createdAt)}
        </p>
        {account.isActive && (
          <p className="text-green flex items-center gap-1 text-sm/5">
            <CheckIcon className="size-4" />
            Active
          </p>
        )}
        {!account.isActive && (
          <p className="text-gray flex items-center gap-1 text-sm/5">
            <ArchiveIcon className="size-4" />
            Archived
          </p>
        )}

        <DropdownMenu>
          <DropdownMenuTrigger className="ms-auto">
            <MoreHorizontalIcon className="text-gray hover:bg-gray/5 size-5 cursor-pointer rounded-xs" />
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={() => editAccount(account)}>
              <Edit2Icon className="mr-1 inline-block size-3" />
              Edit account
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => deleteAccount(account)} variant="destructive">
              <Trash2Icon className="mr-1 inline-block size-3" />
              Delete
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      <div>
        <h2 className="text-foreground text-3xl/8 font-bold">{account.name}</h2>
        {account.description && <p className="mt-1 line-clamp-2 text-base text-gray-600">{account.description}</p>}
      </div>

      <div className="flex gap-12">
        <DefinitionBlock title="Current Balance">
          <p className="flex items-end gap-2">
            {formatCurrency(account.currency, account.currentBalance)}
            {account.currency !== baseCurrency && (
              <span className="text-gray text-base font-normal">
                ({formatCurrency(baseCurrency, account.baseCurrentBalance)})
              </span>
            )}
          </p>
        </DefinitionBlock>
        {account.overdraftLimit && parseFloat(account.overdraftLimit) > 0 && (
          <DefinitionBlock title="Overdraft Limit">
            {formatCurrency(account.currency, account.overdraftLimit)}
          </DefinitionBlock>
        )}
      </div>

      <div className="flex items-center gap-4">
        {account.group && <Badge size="lg">{account.group.name}</Badge>}
        <Badge variant="secondary" size="lg">
          {getAccountTypeLabel(account.type)}
        </Badge>
        <Badge variant="secondary" size="lg">
          {account.currency}
        </Badge>
      </div>
    </Box>
  );
}
