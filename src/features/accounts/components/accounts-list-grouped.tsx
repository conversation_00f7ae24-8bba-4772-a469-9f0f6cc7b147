import type { Account, AccountGroup } from "~/api/types.gen";

import { useMemo } from "react";

import { groupAccountsByGroup } from "../utils";
import AccountCard from "./account-card";
import AccountGroupSection from "./account-group-section";

interface Props {
  accounts: Account[];
  accountGroups: AccountGroup[];
}

export default function AccountsListGrouped(props: Props) {
  const { accounts, accountGroups } = props;

  const groupedData = useMemo(() => groupAccountsByGroup(accounts, accountGroups), [accounts, accountGroups]);

  return (
    <div className="space-y-8">
      {/* Grouped accounts */}
      {groupedData.grouped.map(({ group, accounts }) => (
        <AccountGroupSection key={group.id} group={group} accounts={accounts} />
      ))}

      {/* Ungrouped accounts */}
      {groupedData.ungrouped.length > 0 && (
        <div className="space-y-3">
          <h2 className="text-foreground font-semibold">Ungrouped Accounts</h2>
          {groupedData.ungrouped.map((account) => (
            <AccountCard key={account.id} account={account} />
          ))}
        </div>
      )}
    </div>
  );
}
