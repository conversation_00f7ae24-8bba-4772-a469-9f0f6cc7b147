import type { Account } from "~/api/types.gen";

import AccountCard from "./account-card";

interface Props {
  accounts: Account[];
}

export default function AccountsListFlat(props: Props) {
  const { accounts } = props;

  return (
    <div className="grid grid-cols-1 gap-4 lg:grid-cols-2">
      {accounts.map((account) => (
        <AccountCard key={account.id} account={account} />
      ))}
    </div>
  );
}
