import type { Account, AccountGroup } from "~/api/types.gen";

import { useState } from "react";

import { ChevronDownIcon, ChevronRightIcon, Edit2Icon, MoreHorizontalIcon, PlusIcon, Trash2Icon } from "lucide-react";

import { But<PERSON> } from "~/components/ui/button";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "~/components/ui/collapsible";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "~/components/ui/dropdown-menu";
import { useBaseCurrency } from "~/features/auth/hooks";
import { formatCurrency } from "~/lib/formatters";

import { useAccountGroupActions } from "../hooks";
import AccountRow from "./account-row";

interface Props {
  group: AccountGroup;
  accounts: Account[];
  defaultOpen?: boolean;
}

export default function AccountGroupSection(props: Props) {
  const { group, accounts, defaultOpen = true } = props;

  const baseCurrency = useBaseCurrency();
  const { editAccountGroup, deleteAccountGroup, addAccountToGroup } = useAccountGroupActions();

  const [isOpen, setOpen] = useState(defaultOpen);

  const totalBalance = accounts.reduce((sum, account) => {
    return sum + parseFloat(account.baseCurrentBalance || "0");
  }, 0);

  return (
    <Collapsible open={isOpen} onOpenChange={setOpen}>
      <div style={{ borderLeftColor: group.color || "#ffffff", borderLeftWidth: 4 }}>
        <div className="bg-card flex items-center justify-between rounded-r-lg px-2 py-3">
          <div className="flex items-center gap-1">
            <CollapsibleTrigger asChild>
              <Button variant="ghost" size="icon">
                {isOpen ? <ChevronDownIcon className="size-4" /> : <ChevronRightIcon className="size-4" />}
              </Button>
            </CollapsibleTrigger>

            <div className="flex items-center gap-1">
              {group.iconUrl && <img src={group.iconUrl} alt="" className="size-5" />}
              <div className="flex flex-col items-start">
                <h2 className="text-foreground text-xl/6 font-semibold">{group.name}</h2>
              </div>
            </div>
          </div>

          <div className="flex items-center gap-4">
            <div className="w-16 text-left">
              <p className="text-gray text-xs/5 uppercase">Acc.</p>
              <div className="text-foreground text-xl/6 font-semibold">{accounts.length}</div>
            </div>

            <div className="w-40 text-right">
              <p className="text-gray text-xs/5 uppercase">Total balance</p>
              <div className="text-foreground text-xl/6 font-semibold">
                {formatCurrency(baseCurrency, totalBalance.toFixed(4))}
              </div>
            </div>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon">
                  <MoreHorizontalIcon className="size-4" />
                  <span className="sr-only">Group actions</span>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => addAccountToGroup(group)}>
                  <PlusIcon className="mr-2 size-4" />
                  Add Account
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => editAccountGroup(group)}>
                  <Edit2Icon className="mr-2 size-4" />
                  Edit Group
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => deleteAccountGroup(group)} variant="destructive">
                  <Trash2Icon className="mr-2 size-4" />
                  Delete Group
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </div>

      <CollapsibleContent>
        <div className="mt-2 space-y-1">
          {accounts.length === 0 ? (
            <div className="text-muted-foreground py-8 text-center">
              <p>No accounts in this group</p>
              <Button variant="outline" size="sm" className="mt-2" onClick={() => addAccountToGroup(group)}>
                <PlusIcon className="mr-2 size-4" />
                Add Account
              </Button>
            </div>
          ) : (
            accounts.map((account) => <AccountRow key={account.id} account={account} />)
          )}
        </div>
      </CollapsibleContent>
    </Collapsible>
  );
}
