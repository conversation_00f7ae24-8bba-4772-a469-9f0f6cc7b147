import type { AccountGroupCreateRequest, AccountGroupUpdateRequest } from "~/api/types.gen";

import { useEffect } from "react";
import { useForm } from "react-hook-form";

import { zodResolver } from "@hookform/resolvers/zod";

import { zAccountGroupCreateRequest, zAccountGroupUpdateRequest } from "~/api/zod.gen";
import { InputColor, InputText } from "~/components/inputs";
import { Button } from "~/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "~/components/ui/dialog";
import { Form } from "~/components/ui/form";

import { useAccountGroupActions } from "../hooks";
import { useAccountActionsStore } from "../store";

const defaultColor = "#be32cc";

export default function AccountGroupDialog() {
  const { accountGroupDialog } = useAccountActionsStore();
  const { createAccountGroupMutation, updateAccountGroupMutation, closeAccountGroupDialog } = useAccountGroupActions();

  const accountGroup = accountGroupDialog.editingAccountGroup;
  const isEditing = !!accountGroup;

  const form = useForm<AccountGroupCreateRequest | AccountGroupUpdateRequest>({
    resolver: zodResolver(isEditing ? zAccountGroupUpdateRequest : zAccountGroupCreateRequest),
    defaultValues: {
      name: "",
      color: defaultColor,
      iconUrl: "",
    },
  });

  // Reset form when dialog opens/closes or accountGroup changes
  useEffect(() => {
    if (accountGroupDialog.isOpen) {
      if (accountGroup) {
        form.reset({
          name: accountGroup.name,
          color: accountGroup.color ?? "",
          iconUrl: accountGroup.iconUrl ?? "",
        });
      } else {
        form.reset({
          name: "",
          color: defaultColor,
          iconUrl: "",
        });
      }
    }
  }, [accountGroupDialog.isOpen, accountGroup, form]);

  const handleSubmit = form.handleSubmit((data) => {
    // Convert empty strings to null
    const processedData = {
      ...data,
      color: data.color?.trim() || undefined,
      iconUrl: data.iconUrl?.trim() || undefined,
    };

    if (accountGroup) {
      updateAccountGroupMutation(accountGroup.id, processedData);
    } else {
      createAccountGroupMutation(processedData as AccountGroupCreateRequest);
    }
    closeAccountGroupDialog();
  });

  return (
    <Dialog open={accountGroupDialog.isOpen} onOpenChange={closeAccountGroupDialog}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>{isEditing ? "Edit Account Group" : "Create Account Group"}</DialogTitle>
          <DialogDescription>
            {isEditing
              ? "Update the account group details below."
              : "Create a new account group to organize your accounts."}
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={handleSubmit} className="space-y-4">
            <InputText
              control={form.control}
              name="name"
              label="Group Name"
              placeholder="e.g., Bank Accounts, Credit Cards"
            />

            <InputColor
              control={form.control}
              name="color"
              label="Color"
              hint="Optional"
              description="Choose a color to help identify this group"
            />

            <InputText
              control={form.control}
              type="url"
              name="iconUrl"
              label="Icon URL"
              hint="Optional"
              placeholder="https://example.com/icon.png"
              description="URL to an icon image for this group"
            />

            <DialogFooter>
              <Button type="submit" disabled={form.formState.isLoading}>
                {form.formState.isLoading ? "Saving..." : isEditing ? "Update Group" : "Create Group"}
              </Button>
              <Button
                type="button"
                variant="outline"
                onClick={closeAccountGroupDialog}
                disabled={form.formState.isLoading}
              >
                Cancel
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
