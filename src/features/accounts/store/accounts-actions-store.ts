import type { Account, AccountGroup } from "~/api/types.gen";
import type { AccountActionsStore } from "./types";

import { create } from "zustand";
import { devtools } from "zustand/middleware";
import { immer } from "zustand/middleware/immer";

const useAccountActionsStore = create<AccountActionsStore>()(
  devtools(
    immer((set) => ({
      // Initial state
      accountDialog: {
        isOpen: false,
        editingAccount: null,
        preselectedGroupId: null,
      },
      accountGroupDialog: {
        isOpen: false,
        editingAccountGroup: null,
      },

      // Account dialog actions
      openCreateAccountDialog(preselectedGroupId?: string) {
        set(
          (state) => {
            state.accountDialog.isOpen = true;
            state.accountDialog.editingAccount = null;
            state.accountDialog.preselectedGroupId = preselectedGroupId || null;
          },
          undefined,
          "accounts/openCreateAccountDialog"
        );
      },

      openEditAccountDialog(account: Account) {
        set(
          (state) => {
            state.accountDialog.isOpen = true;
            state.accountDialog.editingAccount = account;
            state.accountDialog.preselectedGroupId = null;
          },
          undefined,
          "accounts/openEditAccountDialog"
        );
      },

      closeAccountDialog() {
        set(
          (state) => {
            state.accountDialog.isOpen = false;
            state.accountDialog.editingAccount = null;
            state.accountDialog.preselectedGroupId = null;
          },
          undefined,
          "accounts/closeAccountDialog"
        );
      },

      // Account group dialog actions
      openCreateAccountGroupDialog() {
        set(
          (state) => {
            state.accountGroupDialog.isOpen = true;
            state.accountGroupDialog.editingAccountGroup = null;
          },
          undefined,
          "accounts/openCreateAccountGroupDialog"
        );
      },

      openEditAccountGroupDialog(accountGroup: AccountGroup) {
        set(
          (state) => {
            state.accountGroupDialog.isOpen = true;
            state.accountGroupDialog.editingAccountGroup = accountGroup;
          },
          undefined,
          "accounts/openEditAccountGroupDialog"
        );
      },

      closeAccountGroupDialog() {
        set(
          (state) => {
            state.accountGroupDialog.isOpen = false;
            state.accountGroupDialog.editingAccountGroup = null;
          },
          undefined,
          "accounts/closeAccountGroupDialog"
        );
      },

      // Utility actions

      addAccountToGroup(accountGroup: AccountGroup) {
        set(
          (state) => {
            state.accountDialog.isOpen = true;
            state.accountDialog.editingAccount = null;
            state.accountDialog.preselectedGroupId = accountGroup.id;
          },
          undefined,
          "accounts/addAccountToGroup"
        );
      },
    })),
    {
      name: "accountActionsStore",
    }
  )
);

export default useAccountActionsStore;
