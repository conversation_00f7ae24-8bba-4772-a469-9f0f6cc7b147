import type { Account, AccountGroup } from "~/api/types.gen";

export interface AccountDialogState {
  isOpen: boolean;
  editingAccount: Account | null;
  preselectedGroupId: string | null;
}

export interface AccountGroupDialogState {
  isOpen: boolean;
  editingAccountGroup: AccountGroup | null;
}

export interface AccountActionsState {
  accountDialog: AccountDialogState;
  accountGroupDialog: AccountGroupDialogState;
}

export interface AccountActionsActions {
  // Account dialog actions
  openCreateAccountDialog: (preselectedGroupId?: string) => void;
  openEditAccountDialog: (account: Account) => void;
  closeAccountDialog: () => void;

  // Account group dialog actions
  openCreateAccountGroupDialog: () => void;
  openEditAccountGroupDialog: (accountGroup: AccountGroup) => void;
  closeAccountGroupDialog: () => void;

  // Utility actions
  addAccountToGroup: (accountGroup: AccountGroup) => void;
}

export type AccountActionsStore = AccountActionsState & AccountActionsActions;
