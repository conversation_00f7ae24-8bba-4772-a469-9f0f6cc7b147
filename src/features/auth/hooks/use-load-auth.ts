import { useEffect } from "react";

import { useQuery, useQueryClient } from "@tanstack/react-query";
import { useShallow } from "zustand/shallow";

import { getCurrentUserOptions } from "~/api/@tanstack/react-query.gen";
import router from "~/router";

import useAuthStore from "../store";

export function useLoadAuth() {
  const queryClient = useQueryClient();

  const { setUser, setAuthenticated } = useAuthStore(
    useShallow((state) => ({ setUser: state.setUser, setAuthenticated: state.setAuthenticated }))
  );

  const action = useQuery({ ...getCurrentUserOptions(), retry: false, refetchOnWindowFocus: true });
  const { data, isLoading, error, refetch: loadAuth } = action;

  useEffect(() => {
    setUser(data && !error ? data : null);
    setAuthenticated(!!data && !error);

    router.invalidate();
  }, [data, error, setUser, setAuthenticated, queryClient]);

  return { isLoading, error, loadAuth };
}
