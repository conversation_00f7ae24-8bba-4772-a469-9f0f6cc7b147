import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useNavigate, useSearch } from "@tanstack/react-router";
import { toast } from "sonner";

import { getCurrentUserQueryKey, getTokenMutation } from "~/api/@tanstack/react-query.gen";

export function useLogin() {
  const navigate = useNavigate();
  const searchParams = useSearch({ strict: false });
  const queryClient = useQueryClient();

  const action = useMutation({
    ...getTokenMutation(),
    onSuccess: async () => {
      await queryClient.invalidateQueries({ queryKey: getCurrentUserQueryKey() });
      await navigate({ to: searchParams.next ?? "/" });

      toast("Welcome back!", { description: "Have a great day!" });
    },
  });

  const { mutate: login, error, isPending: isProcessing } = action;

  return { login, error, isProcessing };
}
