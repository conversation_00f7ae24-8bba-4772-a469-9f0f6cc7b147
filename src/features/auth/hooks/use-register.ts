import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useNavigate } from "@tanstack/react-router";
import { toast } from "sonner";

import { createUserMutation, getCurrentUserQueryKey } from "~/api/@tanstack/react-query.gen";

export function useRegister() {
  const navigate = useNavigate();
  const queryClient = useQueryClient();

  const action = useMutation({
    ...createUserMutation(),
    onSuccess: async () => {
      await queryClient.invalidateQueries({ queryKey: getCurrentUserQueryKey() });
      await navigate({ to: "/" });

      toast("Account created successfully!", { description: "Welcome to Finanze.Pro!" });
    },
  });

  const { mutate: register, error, isPending: isProcessing } = action;

  return { register, error, isProcessing };
}
