import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { useShallow } from "zustand/shallow";

import { logoutMutation } from "~/api/@tanstack/react-query.gen";

import useAuthStore from "../store";

export function useLogout() {
  const queryClient = useQueryClient();

  const { setUser, setAuthenticated } = useAuthStore(
    useShallow((state) => ({ setUser: state.setUser, setAuthenticated: state.setAuthenticated }))
  );

  const action = useMutation({
    ...logoutMutation(),
    onSuccess: () => {
      setUser(null);
      setAuthenticated(false);

      toast("See you soon!", { description: "You have been logged out." });

      return queryClient.clear();
    },
  });

  const { mutate: logout, error, isPending: isProcessing } = action;

  return { logout, error, isProcessing };
}
