import type { Actions, State } from "./types";

import { create } from "zustand";
import { devtools } from "zustand/middleware";
import { immer } from "zustand/middleware/immer";

const useAuthStore = create<State & Actions>()(
  devtools(
    immer((set) => ({
      isAuthenticated: false,
      user: null,

      setUser(user) {
        set(
          (state) => {
            state.user = user;
          },
          undefined,
          "auth/setUser"
        );
      },
      setAuthenticated(isAuthenticated) {
        set(
          (state) => {
            state.isAuthenticated = isAuthenticated;
          },
          undefined,
          "auth/setAuthenticated"
        );
      },
    })),
    {
      name: "authStore",
    }
  )
);

export default useAuthStore;
