import { useForm } from "react-hook-form";

import { zod<PERSON><PERSON>ol<PERSON> } from "@hookform/resolvers/zod";
import { Link } from "@tanstack/react-router";
import { LoaderIcon } from "lucide-react";

import { zUserCreateRequest } from "~/api/zod.gen";
import { ErrorMessage } from "~/components/blocks";
import { InputCurrency, InputText } from "~/components/inputs";
import { Button } from "~/components/ui/button";
import { Form } from "~/components/ui/form";

import { useRegister } from "../hooks";

export interface Props {
  className?: string;
}

export default function RegisterForm({ className }: Props) {
  const { register, error, isProcessing } = useRegister();

  const form = useForm({
    defaultValues: { email: "", password: "", name: null, baseCurrency: "USD" },
    resolver: zodResolver(zUserCreateRequest),
  });

  const onSubmit = form.handleSubmit((data) => {
    register({ body: data });
  });

  return (
    <div className={className}>
      <Form {...form}>
        <form onSubmit={onSubmit} className="min-w-64">
          <div className="flex flex-col gap-2">
            {error && <ErrorMessage title="Registration failed" error={error} />}

            <InputText control={form.control} name="email" type="email" placeholder="Email" autoComplete="username" />
            <InputText
              control={form.control}
              name="password"
              type="password"
              placeholder="Password"
              autoComplete="new-password"
            />
            <InputCurrency control={form.control} name="baseCurrency" placeholder="Base currency" />
          </div>

          <div className="flex flex-col gap-2 pt-8">
            <Button type="submit" className="w-full" disabled={isProcessing}>
              {isProcessing ? <LoaderIcon className="animate-spin" /> : "Create account"}
            </Button>
            <Button variant="outline" disabled={isProcessing} asChild>
              <Link to="/login">Log in</Link>
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}
