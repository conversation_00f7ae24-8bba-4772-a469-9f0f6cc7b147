import { useForm } from "react-hook-form";

import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { Link } from "@tanstack/react-router";
import { LoaderIcon } from "lucide-react";

import { zTokenRequest } from "~/api/zod.gen";
import { ErrorMessage } from "~/components/blocks";
import { InputText } from "~/components/inputs";
import { Button } from "~/components/ui/button";
import { Form } from "~/components/ui/form";

import { useLogin } from "../hooks";

export interface Props {
  className?: string;
}

export default function LoginForm({ className }: Props) {
  const { login, error, isProcessing } = useLogin();

  const form = useForm({
    defaultValues: { email: "", password: "" },
    resolver: zodResolver(zTokenRequest),
  });

  const onSubmit = form.handleSubmit((data) => {
    login({ body: data });
  });

  return (
    <div className={className}>
      <Form {...form}>
        <form onSubmit={onSubmit} className="min-w-64">
          <div className="flex flex-col gap-2">
            {error && <ErrorMessage title="Login failed" error={error} />}

            <InputText control={form.control} name="email" type="email" placeholder="Email" autoComplete="username" />
            <InputText
              control={form.control}
              name="password"
              type="password"
              placeholder="Password"
              autoComplete="current-password"
            />
          </div>

          <div className="flex flex-col gap-2 pt-8">
            <Button type="submit" className="w-full" disabled={isProcessing}>
              {isProcessing ? <LoaderIcon className="animate-spin" /> : "Login"}
            </Button>
            <Button variant="outline" disabled={isProcessing} asChild>
              <Link to="/register">Register</Link>
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}
