import type { Category } from "~/api/types.gen";
import type { CategoryActionsStore } from "./types";

import { create } from "zustand";
import { devtools } from "zustand/middleware";
import { immer } from "zustand/middleware/immer";

const useCategoryActionsStore = create<CategoryActionsStore>()(
  devtools(
    immer((set) => ({
      // Initial state
      categoryDialog: {
        isOpen: false,
        editingCategory: null,
      },

      // Category dialog actions
      openCreateCategoryDialog() {
        set(
          (state) => {
            state.categoryDialog.isOpen = true;
            state.categoryDialog.editingCategory = null;
          },
          undefined,
          "categories/openCreateCategoryDialog"
        );
      },

      openEditCategoryDialog(category: Category) {
        set(
          (state) => {
            state.categoryDialog.isOpen = true;
            state.categoryDialog.editingCategory = category;
          },
          undefined,
          "categories/openEditCategoryDialog"
        );
      },

      closeCategoryDialog() {
        set(
          (state) => {
            state.categoryDialog.isOpen = false;
            state.categoryDialog.editingCategory = null;
          },
          undefined,
          "categories/closeCategoryDialog"
        );
      },
    })),
    {
      name: "categoryActionsStore",
    }
  )
);

export default useCategoryActionsStore;
