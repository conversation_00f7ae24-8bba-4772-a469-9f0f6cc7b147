import type { Category } from "~/api/types.gen";

export interface CategoryDialogState {
  isOpen: boolean;
  editingCategory: Category | null;
}

export interface CategoryActionsState {
  categoryDialog: CategoryDialogState;
}

export interface CategoryActionsActions {
  // Category dialog actions
  openCreateCategoryDialog: () => void;
  openEditCategoryDialog: (category: Category) => void;
  closeCategoryDialog: () => void;
}

export type CategoryActionsStore = CategoryActionsState & CategoryActionsActions;
