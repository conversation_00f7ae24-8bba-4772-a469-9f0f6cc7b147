import type { Category, CategoriesGroupedData } from "./types";

/**
 * Groups categories by type (expense/income)
 */
export function groupCategoriesByType(categories: Category[]): CategoriesGroupedData {
  return categories.reduce(
    (acc, category) => {
      if (category.isExpense) {
        acc.expense.push(category);
      } else {
        acc.income.push(category);
      }
      return acc;
    },
    { expense: [], income: [] } as CategoriesGroupedData
  );
}

/**
 * Gets the display label for category type
 */
export function getCategoryTypeLabel(isExpense: boolean): string {
  return isExpense ? "Expense" : "Income";
}

/**
 * Gets the emoji icon for category type
 */
export function getCategoryTypeIcon(isExpense: boolean): string {
  return isExpense ? "💸" : "💰";
}

/**
 * Filters categories by search query
 */
export function filterCategories(categories: Category[], searchQuery: string): Category[] {
  if (!searchQuery.trim()) {
    return categories;
  }

  const query = searchQuery.toLowerCase();
  return categories.filter((category) =>
    category.name.toLowerCase().includes(query)
  );
}
