import type { CategoryCreateRequest } from "~/api/types.gen";

import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

import { createCategoryMutation, listCategoriesQueryKey } from "~/api/@tanstack/react-query.gen";

import { useCategoryActionsStore } from "../store";

export function useCreateCategory() {
  const queryClient = useQueryClient();

  const { closeCategoryDialog } = useCategoryActionsStore();

  const mutation = useMutation({
    ...createCategoryMutation(),
    onSuccess: (data) => {
      // Invalidate categories query to refresh the list
      void queryClient.invalidateQueries({ queryKey: listCategoriesQueryKey() });

      toast.success("Category created successfully!", {
        description: `${data.name} has been added to your categories.`,
      });

      closeCategoryDialog();
    },
    onError: (error) => {
      toast.error("Failed to create category", {
        description: error.message || "An unexpected error occurred.",
      });
    },
  });

  const createCategory = (data: CategoryCreateRequest) => {
    mutation.mutate({
      body: {
        name: data.name,
        color: data.color || undefined,
        icon: data.icon || undefined,
        isExpense: data.isExpense ?? true, // Default to expense
      },
    });
  };

  return { createCategory, isLoading: mutation.isPending, error: mutation.error };
}
