import { useMemo } from "react";

import { useQuery } from "@tanstack/react-query";

import { listCategoriesOptions } from "~/api/@tanstack/react-query.gen";

import { groupCategoriesByType } from "../utils";

export function useCategories() {
  const { data: categoriesData, isLoading, error, refetch } = useQuery(listCategoriesOptions());

  const categories = useMemo(() => categoriesData ?? [], [categoriesData]);
  const groupedCategories = useMemo(() => groupCategoriesByType(categories), [categories]);

  return { categories, groupedCategories, isLoading, error, refetch };
}
