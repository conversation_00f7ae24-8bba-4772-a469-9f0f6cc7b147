import type { Category } from "../types";

import { <PERSON> } from "@tanstack/react-router";
import { ExternalLinkIcon } from "lucide-react";

import { Button } from "~/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";

interface Props {
  category: Category;
  className?: string;
}

export default function CategoryDetailsTransactions({ category, className }: Props) {
  return (
    <Card className={className}>
      <CardHeader className="flex items-center justify-between gap-4">
        <CardTitle>Recent Transactions</CardTitle>
        <Button variant="ghost" size="sm" asChild>
          <Link to="/transactions" search={{ categoryId: category.id }}>
            <ExternalLinkIcon className="mr-2 size-4" />
            View All
          </Link>
        </Button>
      </CardHeader>
      <CardContent>
        <div className="text-muted-foreground py-8 text-center">
          <p>Coming soon</p>
        </div>
      </CardContent>
    </Card>
  );
}
