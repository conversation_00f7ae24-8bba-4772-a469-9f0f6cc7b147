import type { CategoriesGroupedData, Category } from "../types";

import { useState } from "react";

import { PlusIcon, SearchIcon } from "lucide-react";

import { LoadingIndicator } from "~/components/elements";
import { Button } from "~/components/ui/button";
import { Input } from "~/components/ui/input";

import { useCategoryActions } from "../hooks";
import { filterCategories } from "../utils";
import CategoriesListGroup from "./categories-list-group";

interface Props {
  categories: Category[];
  groupedCategories: CategoriesGroupedData;
  isLoading: boolean;
}

export default function CategoriesList({ categories, groupedCategories, isLoading }: Props) {
  const [searchQuery, setSearchQuery] = useState("");
  const { createCategory } = useCategoryActions();

  // Filter categories based on search query
  const filteredExpenseCategories = filterCategories(groupedCategories.expense, searchQuery);
  const filteredIncomeCategories = filterCategories(groupedCategories.income, searchQuery);
  const hasResults = filteredExpenseCategories.length > 0 || filteredIncomeCategories.length > 0;

  if (isLoading) {
    return <LoadingIndicator />;
  }

  return (
    <div className="space-y-6">
      {/* Header with search and add button */}
      <div className="flex flex-col gap-8 sm:flex-row sm:items-center sm:justify-between">
        <div className="relative max-w-md flex-1">
          <SearchIcon className="text-muted-foreground absolute top-1/2 left-3 size-4 -translate-y-1/2" />
          <Input
            placeholder="Search categories..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-9"
          />
        </div>

        <Button onClick={createCategory}>
          <PlusIcon className="mr-2 size-4" />
          Add Category
        </Button>
      </div>

      {categories.length === 0 && (
        <div className="py-12 text-center">
          <p className="text-muted-foreground mb-4">No categories found</p>
          <Button onClick={createCategory}>
            <PlusIcon className="mr-2 h-4 w-4" />
            Create your first category
          </Button>
        </div>
      )}

      {!hasResults && searchQuery && (
        <div className="py-12 text-center">
          <p className="text-muted-foreground">No categories match your search</p>
        </div>
      )}

      {categories.length > 0 && hasResults && (
        <div className="grid grid-cols-1 gap-8 lg:grid-cols-2">
          <CategoriesListGroup
            categories={filteredIncomeCategories}
            title="Income"
            searchQuery={searchQuery}
            className="lg:order-2"
          />
          <CategoriesListGroup
            categories={filteredExpenseCategories}
            title="Expenses"
            searchQuery={searchQuery}
            className="lg:order-1"
          />
        </div>
      )}
    </div>
  );
}
