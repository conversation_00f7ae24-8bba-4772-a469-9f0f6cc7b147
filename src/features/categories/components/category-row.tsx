import type { Category } from "~/api/types.gen";

import { Link } from "@tanstack/react-router";
import { Edit2Icon, MoreHorizontalIcon, Trash2Icon } from "lucide-react";

import { Button } from "~/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "~/components/ui/dropdown-menu";

import { useCategoryActions } from "../hooks";
import CategoryIcon from "./category-icon";

interface Props {
  category: Category;
}

export default function CategoryRow({ category }: Props) {
  const { editCategory, deleteCategory } = useCategoryActions();

  return (
    <div>
      <div className="flex items-center gap-4 rounded-lg bg-white py-4 ps-4 pe-2">
        <div className="flex flex-grow items-center gap-4">
          <CategoryIcon className="size-5" category={category} />

          <Link
            to="/categories/$categoryId"
            params={{ categoryId: category.id }}
            className="block text-sm/5 hover:underline"
          >
            {category.name}
          </Link>
        </div>

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="icon" className="place-self-start">
              <MoreHorizontalIcon className="size-4" />
              <span className="sr-only">Open menu</span>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={() => editCategory(category)}>
              <Edit2Icon className="mr-1" />
              Edit category
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => deleteCategory(category)} variant="destructive">
              <Trash2Icon className="mr-1" />
              Delete
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  );
}
