import type { Category } from "~/api/types.gen";

import { cn } from "~/lib/utils";

import CategoryRow from "./category-row";

interface Props {
  categories: Category[];
  title: string;
  searchQuery?: string;
  className?: string;
}

export default function CategoriesListGroup({ categories, title, searchQuery, className }: Props) {
  const hasCategories = categories.length > 0;
  const noCategories = categories.length == 0;

  return (
    <div className={cn("space-y-4", className)}>
      <h2 className="text-xl font-semibold">
        <span>{title}</span> <span className="text-gray">({categories.length})</span>
      </h2>

      {noCategories && (
        <div className="text-muted-foreground py-8 text-center">
          {searchQuery ? "No categories match your search" : "No categories yet"}
        </div>
      )}

      {hasCategories && (
        <div className="space-y-3">
          {categories.map((category) => (
            <CategoryRow key={category.id} category={category} />
          ))}
        </div>
      )}
    </div>
  );
}
