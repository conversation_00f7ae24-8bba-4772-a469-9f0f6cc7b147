import type { CategoryUpdateRequest } from "~/api/types.gen";
import type { Category } from "../types";

import { useEffect, useMemo } from "react";
import { useForm } from "react-hook-form";

import { zodResolver } from "@hookform/resolvers/zod";
import { LoaderIcon } from "lucide-react";

import { zCategoryUpdateRequest } from "~/api/zod.gen";
import { InputColor, InputSwitch, InputText } from "~/components/inputs";
import { Button } from "~/components/ui/button";
import { DialogFooter } from "~/components/ui/dialog";
import { Form } from "~/components/ui/form";

import { useCategoryActions, useUpdateCategory } from "../hooks";

interface Props {
  category: Category;
}

export default function CategoryFormUpdate(props: Props) {
  const { category } = props;

  const { updateCategoryMutation, closeCategoryDialog } = useCategoryActions();
  const { isLoading } = useUpdateCategory();

  const defaultValues = useMemo<CategoryUpdateRequest>(
    () => ({
      name: category.name,
      isExpense: category.isExpense,
      color: category.color || "",
      icon: category.icon || "",
    }),
    [category]
  );

  const form = useForm<CategoryUpdateRequest>({
    resolver: zodResolver(zCategoryUpdateRequest),
    defaultValues,
  });

  const resetForm = form.reset;

  // Reset form when category changes
  useEffect(() => {
    resetForm(defaultValues);
  }, [defaultValues, resetForm]);

  const handleSubmit = form.handleSubmit((data: CategoryUpdateRequest) => {
    // Convert empty strings to null/undefined
    const processedData = {
      ...data,
      color: data.color?.trim() || undefined,
      icon: data.icon?.trim() || undefined,
    };

    updateCategoryMutation(category.id, processedData);
  });

  return (
    <Form {...form}>
      <form onSubmit={handleSubmit} className="space-y-4">
        <InputText control={form.control} name="name" label="Category Name" placeholder="e.g., Groceries, Salary" />

        <InputSwitch
          control={form.control}
          name="isExpense"
          label="Is expense?"
          description="Expenses are outgoing, income is incoming"
        />

        <InputColor
          control={form.control}
          name="color"
          label="Color"
          hint="Optional"
          description="Choose a color to help identify this category"
        />

        <InputText
          control={form.control}
          name="icon"
          label="Icon"
          hint="Optional"
          placeholder="e.g., 🍔, 💰, 🏠"
          description="Add an emoji icon for visual identification"
        />

        <DialogFooter>
          <Button type="submit" disabled={isLoading}>
            {isLoading ? <LoaderIcon className="animate-spin" /> : "Update Category"}
          </Button>
          <Button type="button" variant="outline" onClick={closeCategoryDialog}>
            Cancel
          </Button>
        </DialogFooter>
      </form>
    </Form>
  );
}
