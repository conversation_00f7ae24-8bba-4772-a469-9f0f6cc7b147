import type { CategoryCreateRequest } from "~/api/types.gen";

import { useForm } from "react-hook-form";

import { zodResolver } from "@hookform/resolvers/zod";
import { LoaderIcon } from "lucide-react";

import { zCategoryCreateRequest } from "~/api/zod.gen";
import { InputColor, InputSwitch, InputText } from "~/components/inputs";
import { Button } from "~/components/ui/button";
import { DialogFooter } from "~/components/ui/dialog";
import { Form } from "~/components/ui/form";

import { defaultColor } from "../constants";
import { useCategoryActions, useCreateCategory } from "../hooks";

export default function CategoryFormCreate() {
  const { createCategoryMutation, closeCategoryDialog } = useCategoryActions();
  const { isLoading } = useCreateCategory();

  const form = useForm<CategoryCreateRequest>({
    resolver: zodResolver(zCategoryCreateRequest),
    defaultValues: {
      name: "",
      color: defaultColor,
      icon: "",
      isExpense: true,
    },
  });

  const handleSubmit = form.handleSubmit((data: CategoryCreateRequest) => {
    // Convert empty strings to null/undefined
    const processedData = {
      ...data,
      color: data.color?.trim() || undefined,
      icon: data.icon?.trim() || undefined,
    };

    createCategoryMutation(processedData);
  });

  return (
    <Form {...form}>
      <form onSubmit={handleSubmit} className="space-y-4">
        <InputText control={form.control} name="name" label="Category Name" placeholder="e.g., Groceries, Salary" />

        <InputSwitch
          control={form.control}
          name="isExpense"
          label="Is expense?"
          description="Expenses are outgoing, income is incoming"
        />

        <InputColor
          control={form.control}
          name="color"
          label="Color"
          hint="Optional"
          description="Choose a color to help identify this category"
        />

        <InputText
          control={form.control}
          name="icon"
          label="Icon"
          hint="Optional"
          placeholder="e.g., 🍔, 💰, 🏠"
          description="Add an emoji icon for visual identification"
        />

        <DialogFooter>
          <Button type="submit" disabled={isLoading}>
            {isLoading ? <LoaderIcon className="animate-spin" /> : "Create category"}
          </Button>
          <Button type="button" variant="outline" onClick={closeCategoryDialog}>
            Cancel
          </Button>
        </DialogFooter>
      </form>
    </Form>
  );
}
