{"name": "webapp", "description": "Finanze.Pro Web Application", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "generate:client": "openapi-ts", "test": "vitest"}, "dependencies": {"@fontsource-variable/inter": "^5.2.6", "@hookform/resolvers": "^5.1.1", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-visually-hidden": "^1.2.3", "@tanstack/react-query": "^5.81.2", "@tanstack/react-query-devtools": "^5.81.2", "@tanstack/react-router": "^1.121.34", "@tanstack/react-router-devtools": "^1.121.34", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "immer": "^10.1.1", "lucide-react": "^0.523.0", "next-themes": "^0.4.6", "react": "^19.1.0", "react-day-picker": "^9.7.0", "react-dom": "^19.1.0", "react-hook-form": "^7.58.1", "sonner": "^2.0.5", "tailwind-merge": "^3.3.1", "zod": "^3.25.67", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/js": "^9.29.0", "@hey-api/openapi-ts": "^0.76.0", "@ianvs/prettier-plugin-sort-imports": "^4.4.2", "@tailwindcss/vite": "^4.1.10", "@tanstack/eslint-plugin-query": "^5.81.2", "@tanstack/router-plugin": "^1.121.34", "@testing-library/react": "^16.3.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react-swc": "^3.10.2", "eslint": "^9.29.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.2.0", "prettier": "^3.6.0", "prettier-plugin-tailwindcss": "^0.6.13", "tailwindcss": "^4.1.10", "tw-animate-css": "^1.3.4", "typescript": "~5.8.3", "typescript-eslint": "^8.35.0", "vite": "^7.0.0", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.2.4"}, "packageManager": "pnpm@10.12.1+sha512.f0dda8580f0ee9481c5c79a1d927b9164f2c478e90992ad268bbb2465a736984391d6333d2c327913578b2804af33474ca554ba29c04a8b13060a717675ae3ac"}