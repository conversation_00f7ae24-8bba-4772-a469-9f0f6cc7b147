# Dependencies
node_modules
.pnpm-store

# Build outputs
dist
build
.cache

# Version control
.git
.gitignore

# IDE and editor files
.vscode
.idea
*.swp
*.swo
.DS_Store

# Environment and config files
.env
.env.*
!.env.example

# Test files
__tests__
*.test.js
*.spec.js
coverage

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Misc
README.md
README.vite.md
.editorconfig
.prettierrc
.wakatime-project
.augment-guidelines
.junie

# Docker related
Dockerfile
.dockerignore
# nginx.conf
