# AGENT GUIDELINES

This document outlines conventions and commands for AI agents operating in this repository.

## Development Commands

### Essential Commands

- `pnpm dev` - Start development server with hot reload
- `pnpm build` - Build for production (runs TypeScript check + Vite build)
- `pnpm lint` - Run ESLint
- `pnpm generate:client` - Regenerate API client from backend OpenAPI spec

### Backend Integration

- Backend must be running on `http://127.0.0.1:5000` for development
- API calls are proxied from `/api` to the backend during development
- Always regenerate the API client after backend API changes

## Architecture Overview

### Technology Stack

- **React 19** with TypeScript and Vite
- **TanStack Router** for file-based routing with type safety
- **TanStack Query** for server state management with auto-generated hooks
- **Zustand** with Immer for client state (auth store)
- **shadcn/ui** components with TailwindCSS
- **React Hook Form** with Zod validation

### Project Structure

- `src/api/` - Auto-generated TypeScript client from OpenAPI spec
- `src/routes/` - File-based routing with `_auth` (protected) and `_guest` (public) layouts
- `src/features/` - Feature modules (auth, etc.) with components, hooks, and stores
- `src/components/` - Reusable UI components organized by type
- `src/lib/` - Utility functions and configurations

### Authentication Architecture

- Protected routes use `_auth` layout which redirects unauthenticated users
- Guest routes use `_guest` layout which redirects authenticated users
- Auth state managed by Zustand store at `src/features/auth/store.ts`
- Auth status persisted and loaded on app initialization

### API Integration Pattern

- OpenAPI spec automatically generates TypeScript client with TanStack Query hooks
- All API types are generated ensuring end-to-end type safety
- Use generated hooks from `src/api/` for data fetching
- Backend OpenAPI endpoint: `http://127.0.0.1:5000/api/openapi.json`

### Component Organization

- Base UI components in `src/components/ui/` (shadcn/ui)
- Feature-specific components in `src/features/[feature]/components/`
- Layout components in `src/components/layouts/`
- Reusable blocks in `src/components/blocks/`

### Path Aliases

- `~/` maps to `src/` directory for clean imports
- Use `~/api/*` for generated API client imports
- Use `~/components/*` for component imports
