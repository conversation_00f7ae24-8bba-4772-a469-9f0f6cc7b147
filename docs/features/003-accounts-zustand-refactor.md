# 🏗️ Accounts Feature Zustand Refactoring

## Overview

This document describes the refactoring of the accounts feature to eliminate prop drilling by implementing Zustand store and React hooks for state management and actions.

## Problem

Previously, all account and account group actions (create, edit, delete) were passed down through multiple component layers via props, creating:

- **Prop drilling**: Actions had to be passed from the route component through `AccountsList` → `AccountsListGrouped`/`AccountsListFlat` → `AccountCard`/`AccountRow`/`AccountGroupSection`
- **Tight coupling**: Components were tightly coupled to their parent's action handlers
- **Reduced reusability**: Components couldn't be used independently without providing all action props
- **Complex interfaces**: Component props interfaces were cluttered with action handlers

## Solution

### 1. Zustand Store (`useAccountActionsStore`)

Created a centralized store to manage dialog states:

```typescript
interface AccountActionsState {
  accountDialog: {
    isOpen: boolean;
    editingAccount: Account | null;
    preselectedGroupId: string | null;
  };
  accountGroupDialog: {
    isOpen: boolean;
    editingAccountGroup: AccountGroup | null;
  };
}
```

**Location**: `src/features/accounts/store/`

### 2. Action Hooks

#### `useAccountActions`
Provides account-related actions:
- `createAccount()` - Opens create account dialog
- `editAccount(account)` - Opens edit account dialog
- `deleteAccount(account)` - Shows confirmation and deletes account
- `closeAccountDialog()` - Closes account dialog
- Direct mutation functions for form submissions

#### `useAccountGroupActions`
Provides account group-related actions:
- `createAccountGroup()` - Opens create account group dialog
- `editAccountGroup(group)` - Opens edit account group dialog
- `deleteAccountGroup(group)` - Shows confirmation and deletes group
- `addAccountToGroup(group)` - Opens account dialog with preselected group
- `closeAccountGroupDialog()` - Closes account group dialog
- Direct mutation functions for form submissions

**Location**: `src/features/accounts/hooks/`

### 3. Component Refactoring

#### Before
```typescript
// AccountsList component
interface Props {
  accounts: Account[];
  accountGroups: AccountGroup[];
  onCreateAccount: () => void;
  onCreateAccountGroup: () => void;
  onEditAccount: (account: Account) => void;
  onDeleteAccount: (account: Account) => void;
  onEditAccountGroup: (group: AccountGroup) => void;
  onDeleteAccountGroup: (group: AccountGroup) => void;
  onAddAccountToGroup: (group: AccountGroup) => void;
  isLoading?: boolean;
}
```

#### After
```typescript
// AccountsList component
interface Props {
  accounts: Account[];
  accountGroups: AccountGroup[];
  isLoading?: boolean;
}

// Inside component
const { createAccount } = useAccountActions();
const { createAccountGroup } = useAccountGroupActions();
```

### 4. Route Component Simplification

#### Before (177 lines)
- Complex state management with multiple `useState` hooks
- Multiple action handler functions
- Prop drilling to child components

#### After (89 lines)
- Minimal state management using store
- Simple form submission handlers
- Clean component props

## Benefits

1. **Eliminated Prop Drilling**: Actions are now accessible directly in any component via hooks
2. **Improved Reusability**: Components can be used independently without requiring action props
3. **Cleaner Interfaces**: Component props are focused on data, not actions
4. **Better Separation of Concerns**: Dialog state management is centralized in the store
5. **Easier Testing**: Components can be tested without mocking complex action props
6. **Consistent Patterns**: Follows the existing confirmation dialog pattern using Zustand

## Usage Examples

### In any component that needs account actions:
```typescript
import { useAccountActions } from "~/features/accounts";

function MyComponent() {
  const { createAccount, editAccount, deleteAccount } = useAccountActions();
  
  return (
    <div>
      <button onClick={createAccount}>Create Account</button>
      <button onClick={() => editAccount(account)}>Edit</button>
      <button onClick={() => deleteAccount(account)}>Delete</button>
    </div>
  );
}
```

### In any component that needs account group actions:
```typescript
import { useAccountGroupActions } from "~/features/accounts";

function MyComponent() {
  const { createAccountGroup, editAccountGroup, addAccountToGroup } = useAccountGroupActions();
  
  return (
    <div>
      <button onClick={createAccountGroup}>Create Group</button>
      <button onClick={() => addAccountToGroup(group)}>Add Account to Group</button>
    </div>
  );
}
```

## Files Modified

- `src/features/accounts/store/` - New store implementation
- `src/features/accounts/hooks/use-account-actions.ts` - New hook
- `src/features/accounts/hooks/use-account-group-actions.ts` - New hook
- `src/features/accounts/components/accounts-list.tsx` - Removed action props
- `src/features/accounts/components/accounts-list-grouped.tsx` - Removed action props
- `src/features/accounts/components/accounts-list-flat.tsx` - Removed action props
- `src/features/accounts/components/account-card.tsx` - Uses hooks instead of props
- `src/features/accounts/components/account-row.tsx` - Uses hooks instead of props
- `src/features/accounts/components/account-group-section.tsx` - Uses hooks instead of props
- `src/features/accounts/components/account-details.tsx` - Uses hooks instead of props
- `src/routes/_auth/accounts.index.tsx` - Simplified route component
- `src/routes/_auth/accounts.$accountId.tsx` - Simplified route component

## Testing

A basic test suite has been added to verify the hooks return the expected functions:
- `src/features/accounts/hooks/__tests__/use-account-actions.test.ts`

## Future Improvements

1. Add more comprehensive tests for the store and hooks
2. Consider adding loading states to the store for better UX
3. Add error handling states to the store
4. Consider extracting dialog management to a reusable pattern for other features
