# 🏷️ Categories Feature Request

## 📋 Overview

This feature request outlines the implementation of a comprehensive transaction categories management system for Finanze.Pro. The feature will enable users to manage their transaction categories, organizing them into expense and income categories for better transaction categorization and financial tracking.

## 🎯 Goals

- Provide users with a centralized view of all their transaction categories
- Enable clear separation between expense and income categories
- Implement full CRUD operations for categories
- Ensure seamless integration with existing transaction features
- Support visual customization with colors and icons

## 🔧 Technical Requirements

### API Integration

The feature will utilize existing API endpoints:

**Categories:**

- `GET /api/v1/categories` - List all categories
- `GET /api/v1/categories/{id}` - Get category details
- `POST /api/v1/categories` - Create new category
- `PUT /api/v1/categories/{id}` - Update category
- `DELETE /api/v1/categories/{id}` - Delete category

### Data Models

**Category:**

```typescript
import type { Category, CategoryCreateRequest, CategoryUpdateRequest } from "@/api/types.gen";
```

The `Category` type includes:

- `id: string` - Unique identifier
- `name: string` - Category name
- `color: string | null` - Optional color for visual identification
- `icon: string | null` - Optional icon identifier
- `isExpense: boolean` - Determines if category is for expenses (true) or income (false)
- `createdAt: string` - Creation timestamp
- `updatedAt: string` - Last update timestamp

## 🖥️ User Interface

### 1. Categories List Page (`/categories`)

**Layout:**

- Page header with title "Categories" and "Add Category" button
- Two-column layout:
  - Left column: "Expense Categories"
  - Right column: "Income Categories"
- Each category shows: name, color indicator, icon (if set)
- Action buttons for each category: Edit, Delete
- Search/filter functionality across both columns

**Features:**

- Visual indicators using colors and icons for quick identification
- Separate sections for expense vs income categories
- Quick category overview with count of categories in each type
- Responsive design that stacks columns on mobile devices
- Empty state messages when no categories exist in either column

### 2. Category Details Page (`/categories/{id}`)

**Layout:**

- Category header with name, type indicator (Expense/Income), and color/icon
- Category information card (type, color, icon, creation date)
- Related transactions list (integration with transactions feature)
- Quick actions: Edit Category, View All Transactions

**Features:**

- Category type badge (Expense/Income) with appropriate styling
- Visual representation of category color and icon
- Statistics about category usage (transaction count, total amounts)
- Recent transactions using this category

### 3. Dialog Components

#### Category Creation Dialog

**Form Fields:**

- Category name (required, max 100 characters)
- Category type (radio buttons: Expense/Income, defaults to Expense)
- Color picker (optional, defaults to #ff6b6b)
- Icon selector (optional, text input for icon identifier)

#### Category Edit Dialog

**Form Fields:**

- Same as creation form, but with current values pre-filled
- Cannot change category type after creation (read-only)

## 🔄 User Workflows

### Creating a Category

1. User clicks "Add Category" button on categories page
2. Category creation dialog opens
3. User fills required fields (name, selects type)
4. User optionally chooses color and icon
5. User submits form
6. Category is created and appears in appropriate column
7. Success toast notification is shown

### Managing Categories

1. User clicks "Edit" button on category card or details page
2. Category edit dialog opens with pre-filled values
3. User modifies desired fields (except type)
4. User submits form
5. Category is updated
6. UI refreshes to show changes
7. Success toast notification is shown

### Organizing Categories

1. Categories are automatically organized by type (expense/income)
2. Users can search across both columns
3. Visual indicators help distinguish categories quickly
4. Categories can be filtered by name or type

## 🎨 Design Considerations

### Visual Hierarchy

- Clear separation between expense and income categories
- Color coding for quick visual identification
- Consistent card layout for categories
- Type indicators with appropriate icons (💸 for expenses, 💰 for income)

### Responsive Design

- Two-column layout on desktop
- Stacked layout on mobile devices
- Touch-friendly buttons and interactions
- Optimized card layouts for different screen sizes

### Accessibility

- Proper ARIA labels and roles
- Keyboard navigation support
- Screen reader friendly content
- High contrast color options
- Clear visual distinction between category types

## 🔗 Integration Points

### Navigation

- Add "Categories" link to main navigation header (positioned after "Accounts")
- Update transaction forms to use category selection
- Update dashboard to show category-based spending insights

### Transaction Integration

- Category selection in transaction creation/editing
- Category-based transaction filtering
- Category spending analytics and reports

## 📱 Implementation Phases

### Phase 1: Core Functionality

- Categories list page with two-column layout
- Category creation and editing dialogs
- Basic category details page
- CRUD operations with proper error handling

### Phase 2: Enhanced Features

- Advanced search and filtering
- Category usage statistics
- Integration with transaction forms
- Visual customization improvements

### Phase 3: Analytics & Integration

- Category-based spending reports
- Dashboard widgets with category insights
- Transaction categorization suggestions
- Enhanced mobile experience

## ✅ Acceptance Criteria

- [ ] Users can view all categories organized by type (expense/income)
- [ ] Users can create, edit, and delete categories
- [ ] Users can distinguish between expense and income categories
- [ ] Users can customize categories with colors and icons
- [ ] Users can view detailed category information and usage
- [ ] All forms include proper validation and error handling
- [ ] UI is responsive and accessible
- [ ] Integration with existing navigation and routing
- [ ] Proper loading states and error handling
- [ ] Success/error notifications for all operations
- [ ] Two-column layout works on desktop and stacks on mobile

## 🚀 Future Enhancements

- Category templates for quick setup
- Subcategory support for hierarchical organization
- Category-based budgeting integration
- Import/export category data
- Category usage analytics and insights
- Smart category suggestions based on transaction descriptions
- Category archiving instead of deletion
- Bulk category operations
