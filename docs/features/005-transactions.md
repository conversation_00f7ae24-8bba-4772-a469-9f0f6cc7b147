# 💸 Transactions Feature Request

## 📋 Overview

This feature request outlines the implementation of a basic transactions management system for Finanze.Pro. The feature will enable users to track their financial transactions including expenses, income, and transfers between accounts. This is an initial implementation focusing on core CRUD operations with a paginated list view.

## 🎯 Goals

- Provide users with a centralized view of all their financial transactions
- Enable creation, viewing, editing, and deletion of transactions
- Support three transaction types: income, expense, and transfer
- Implement paginated transaction listing for performance
- Ensure seamless integration with existing accounts and categories features
- Maintain simple, focused UI without advanced filtering (initial implementation)

## 🔧 Technical Requirements

### API Integration

The feature will utilize existing API endpoints:

**Transactions:**

- `GET /api/v1/transactions` - List transactions (paginated)
- `GET /api/v1/transactions/{id}` - Get transaction details
- `POST /api/v1/transactions` - Create new transaction
- `PUT /api/v1/transactions/{id}` - Update transaction
- `DELETE /api/v1/transactions/{id}` - Delete transaction

### Data Models

**Transaction:**

```typescript
import type {
  TransactionCreateRequest,
  TransactionListResponse,
  TransactionResponse,
  TransactionType,
  TransactionUpdateRequest,
} from "@/api/types.gen";
```

The `TransactionResponse` type includes:

- `id: string` - Unique identifier
- `transactionDate: string` - Date of the transaction
- `categoryId: string | null` - Optional category reference
- `category?: Category` - Category details (if assigned)
- `type: TransactionType` - "income" | "expense" | "transfer"
- `description: string | null` - Optional transaction description
- `accountId: string` - Source account reference
- `account: Account` - Source account details
- `amount: Decimal` - Transaction amount in account currency
- `accountToId: string | null` - Destination account (for transfers)
- `accountTo?: Account` - Destination account details (for transfers)
- `amountTo: Decimal | null` - Amount in destination currency (for transfers)
- `baseAmount: Decimal` - Amount in user's base currency
- `baseAmountTo: Decimal | null` - Destination amount in base currency (for transfers)
- `taskRecordId: string | null` - Optional task record reference
- `createdAt: string` - Creation timestamp
- `updatedAt: string` - Last update timestamp

## 🖥️ User Interface

### 1. Transactions List Page (`/transactions`)

**Layout:**

- Page header with title "💸 Transactions" and "Add Transaction" button
- Paginated transaction list with basic transaction cards
- Each transaction shows:
  - Transaction date
  - Description (or "No description" if empty)
  - Category name (if assigned) with color indicator
  - Account name(s) - source account for income/expense, both accounts for transfers
  - Amount with currency (positive for income, negative for expense)
  - Transaction type indicator (icon or badge)
- Pagination controls at the bottom
- Action buttons for each transaction: Edit, Delete

**Features:**

- Clean, simple list layout focused on essential information
- Visual indicators for transaction types:
  - 📈 Income (green styling)
  - 📉 Expense (red styling)
  - 🔄 Transfer (blue styling)
- Dual currency display when applicable (account currency + base currency)
- Responsive design with optimized mobile layout
- Loading states during data fetching
- Empty state when no transactions exist

### 2. Transaction Details Page (`/transactions/{id}`)

**Layout:**

- Transaction header with type icon, description, and amount
- Transaction information card showing all details
- Related account(s) information with links
- Category information (if assigned)
- Quick actions: Edit Transaction, Delete Transaction

**Features:**

- Complete transaction details display
- Account information with navigation links
- Category display with visual indicators
- Transaction type badge with appropriate styling
- Dual currency amounts for transfers
- Task record reference (if applicable)

### 3. Dialog Components

#### Transaction Creation Dialog

**Form Fields:**

- Transaction date (required, date picker, defaults to today)
- Transaction type (required, radio buttons: Income/Expense/Transfer)
- Source account (required, dropdown with search)
- Amount (required, decimal input with currency display)
- Category (optional, dropdown filtered by transaction type)
- Description (optional, textarea)
- **For transfers only:**
  - Destination account (required, dropdown excluding source account)
  - Destination amount (optional, auto-calculated if same currency)

#### Transaction Edit Dialog

**Form Fields:**

- Same as creation form, but with current values pre-filled
- All fields remain editable
- Proper validation for transfer-specific fields

## 🔄 User Workflows

### Creating a Transaction

1. User clicks "Add Transaction" button on transactions page
2. Transaction creation dialog opens
3. User selects transaction type (income/expense/transfer)
4. User fills required fields (date, account, amount)
5. User optionally selects category and adds description
6. For transfers: user selects destination account and amount
7. User submits form
8. Transaction is created and appears in the list
9. Success toast notification is shown

### Viewing Transactions

1. User navigates to transactions page
2. Paginated list of transactions loads
3. User can navigate through pages using pagination controls
4. User can click on transaction to view details
5. User can perform edit/delete actions from list or details view

### Editing Transactions

1. User clicks "Edit" button on transaction card or details page
2. Transaction edit dialog opens with pre-filled values
3. User modifies desired fields
4. User submits form
5. Transaction is updated
6. UI refreshes to show changes
7. Success toast notification is shown

## 🎨 Design Considerations

### Visual Hierarchy

- Clear transaction type indicators with consistent color coding
- Prominent amount display with appropriate positive/negative styling
- Account and category information clearly visible
- Date formatting for easy scanning

### Responsive Design

- Mobile-first approach with touch-friendly interactions
- Optimized card layouts for different screen sizes
- Simplified mobile view focusing on essential information
- Accessible pagination controls

### Accessibility

- Proper ARIA labels and roles
- Keyboard navigation support
- Screen reader friendly content
- High contrast color options
- Clear visual distinction between transaction types

## 🔗 Integration Points

### Navigation

- Add "Transactions" link to main navigation header (positioned after "Categories")
- Update account details pages to show recent transactions
- Update dashboard to show transaction summary widgets

### Account Integration

- Transaction creation updates account balances automatically
- Account selection in transaction forms
- Account-based transaction filtering (future enhancement)

### Category Integration

- Category selection filtered by transaction type (expense/income)
- Category-based transaction organization (future enhancement)

## 📱 Implementation Phases

### Phase 1: Core Functionality (Current Scope)

- Transactions list page with pagination
- Transaction creation and editing dialogs
- Basic transaction details page
- CRUD operations with proper error handling
- Integration with accounts and categories

### Phase 2: Enhanced Features (Future)

- Advanced filtering and search
- Date range selection
- Transaction import/export
- Bulk operations
- Enhanced mobile experience

### Phase 3: Analytics & Reporting (Future)

- Transaction analytics and insights
- Spending patterns and trends
- Category-based reports
- Account balance history

## ✅ Acceptance Criteria

- [ ] Users can view paginated list of all transactions
- [ ] Users can create transactions of all three types (income/expense/transfer)
- [ ] Users can edit and delete existing transactions
- [ ] Users can view detailed transaction information
- [ ] Transaction forms include proper validation and error handling
- [ ] UI clearly distinguishes between transaction types
- [ ] Pagination works correctly with proper navigation
- [ ] Integration with accounts and categories works seamlessly
- [ ] UI is responsive and accessible
- [ ] Proper loading states and error handling
- [ ] Success/error notifications for all operations
- [ ] Transfer transactions handle dual accounts and currencies correctly

## 🚀 Future Enhancements

- Advanced filtering by date range, amount, category, account
- Search functionality across transaction descriptions
- Bulk transaction operations (delete, categorize, edit)
- Transaction templates for recurring entries
- Import transactions from bank files (CSV, OFX)
- Transaction reconciliation features
- Duplicate transaction detection
- Transaction splitting for multiple categories
- Recurring transaction scheduling
- Enhanced analytics and reporting
- Transaction attachments (receipts, invoices)
