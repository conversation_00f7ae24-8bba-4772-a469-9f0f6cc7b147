# 🔔 Confirmation Dialogs

A reusable confirmation dialog system built with shadcn/ui AlertDialog and Zustand store for managing async confirmation dialogs throughout the application.

## Features

- **Async/Promise-based API** - Use `await` to get user confirmation
- **Customizable content** - Title, description, button text, and styling
- **Destructive variant** - Special styling for dangerous actions
- **Global state management** - Single dialog instance managed by Zustand
- **TypeScript support** - Fully typed API

## Usage

### Basic Usage

```tsx
import { useConfirm } from "~/features/ui/confirmations/hooks";

function MyComponent() {
  const ask = useConfirm();

  const handleDelete = async () => {
    const ok = await ask({
      title: "Remove account",
      description: "Are you sure you want to remove this account?",
    });

    if (ok) {
      // User confirmed - proceed with deletion
      console.log("Deleting account...");
    }
  };

  return <button onClick={handleDelete}>Delete Account</button>;
}
```

### Advanced Usage

```tsx
const handleDangerousAction = async () => {
  const ok = await ask({
    title: "Permanent Action",
    description: "This action cannot be undone. Are you absolutely sure?",
    confirmText: "Yes, Delete Forever",
    cancelText: "Keep It",
    variant: "destructive",
  });

  if (ok) {
    // Proceed with dangerous action
  }
};
```

## API Reference

### `useConfirm()`

Returns a function that shows a confirmation dialog and returns a Promise<boolean>.

### `ConfirmationOptions`

```typescript
interface ConfirmationOptions {
  title: string; // Dialog title (required)
  description?: string; // Optional description text
  confirmText?: string; // Confirm button text (default: "Confirm")
  cancelText?: string; // Cancel button text (default: "Cancel")
  variant?: "default" | "destructive"; // Button styling (default: "default")
}
```

## Implementation Details

### Store Structure

The confirmation system uses a Zustand store with the following state:

- `isOpen: boolean` - Controls dialog visibility
- `options: ConfirmationOptions | null` - Current dialog configuration
- `resolve: ((value: boolean) => void) | null` - Promise resolver function

### Components

- **ConfirmationDialog** - The actual dialog component (automatically included in App.tsx)
- **useConfirm** - Hook that provides the confirmation function

### Integration

The `ConfirmationDialog` component is automatically included in the root App component, so no additional setup is required. Just import and use the `useConfirm` hook anywhere in your application.

## Examples

See `src/features/ui/confirmations/components/example-usage.tsx` for complete working examples of different confirmation dialog patterns.
