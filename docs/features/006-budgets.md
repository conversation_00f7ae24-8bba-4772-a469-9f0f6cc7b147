# 💰 Budgets Feature Request

## 📋 Overview

This feature request outlines the implementation of a comprehensive budget management system for Finanze.Pro. The feature will enable users to create and manage spending budgets with configurable periods, track budget usage through budget records, and monitor their financial goals. Users can set budget limits for specific time periods and optionally restrict budgets to specific accounts.

## 🎯 Goals

- Provide users with tools to set and manage spending budgets
- Support multiple budget periods (week, month, quarter, year) with month as default
- Enable budget tracking through automatically generated budget records
- Support both fixed amount and percentage-based budgets
- Allow optional account restrictions for targeted budget management
- Implement comprehensive CRUD operations for budgets
- Provide paginated budget history for detailed tracking
- Ensure seamless integration with existing accounts system

## 🔧 Technical Requirements

### API Integration

The feature will utilize existing API endpoints:

**Budgets:**

- `GET /api/v1/budgets` - List budgets (with optional archived filter)
- `GET /api/v1/budgets/{id}` - Get budget details
- `POST /api/v1/budgets` - Create new budget
- `PUT /api/v1/budgets/{id}` - Update budget
- `DELETE /api/v1/budgets/{id}` - Delete budget
- `GET /api/v1/budgets/{id}/history` - Get paginated budget records

### Data Models

**Budget Types:**

```typescript
import type {
  Budget,
  BudgetCreateRequest,
  BudgetListResponse,
  BudgetPeriod,
  BudgetRecord,
  BudgetType,
  BudgetUpdateRequest,
  BudgetHistoryResponse,
} from "@/api/types.gen";
```

The `Budget` type includes:

- `id: string` - Unique identifier
- `name: string` - Budget name
- `description: string | null` - Optional budget description
- `period: BudgetPeriod` - "week" | "month" | "quarter" | "year"
- `type: BudgetType` - "fixed" | "percentage"
- `value: Decimal` - Budget amount or percentage value
- `accounts: Array<string> | null` - Optional account ID restrictions
- `isArchived: boolean` - Archive status
- `currentRecord?: BudgetRecord` - Current period's budget record
- `createdAt: string` - Creation timestamp
- `updatedAt: string` - Last update timestamp

The `BudgetRecord` type includes:

- `id: string` - Unique identifier
- `budgetId: string` - Parent budget reference
- `startDate: string` - Period start date
- `endDate: string` - Period end date
- `plannedAmount: Decimal` - Planned budget amount for period
- `usedAmount: Decimal` - Actually used amount in period
- `createdAt: string` - Creation timestamp
- `updatedAt: string` - Last update timestamp

## 🖥️ User Interface

### 1. Budgets List Page (`/budgets`)

**Layout:**

- Page header with title "💰 Budgets" and "Add Budget" button
- Budget cards grid showing active budgets
- Each budget card displays:
  - Budget name and description
  - Budget period and type indicators
  - Current period progress (used/planned amounts)
  - Progress bar with visual indicators (green/yellow/red based on usage)
  - Account restrictions (if any)
  - Quick actions: Edit, Archive/Unarchive, Delete
- Toggle to show/hide archived budgets
- Empty state when no budgets exist

**Features:**

- Visual progress indicators for budget usage
- Color-coded progress bars:
  - 🟢 Green: Under 75% usage
  - 🟡 Yellow: 75-100% usage
  - 🔴 Red: Over 100% usage
- Budget type badges (Fixed Amount / Percentage)
- Period indicators with appropriate icons
- Account restriction chips when applicable
- Responsive grid layout

### 2. Budget Details Page (`/budgets/{id}`)

**Layout:**

- Budget header with name, description, and current period status
- Budget configuration card showing all settings
- Current period summary with progress visualization
- Paginated budget records section showing historical data
- Quick actions: Edit Budget, Archive/Unarchive, Delete Budget

**Features:**

- Comprehensive budget information display
- Current period progress with detailed breakdown
- Historical budget records with pagination
- Account information with navigation links (if restricted)
- Budget performance analytics (usage trends)

### 3. Dialog Components

#### Budget Creation Dialog

**Form Fields:**

- Budget name (required, text input)
- Description (optional, textarea)
- Budget period (required, radio buttons: Week/Month/Quarter/Year, default: Month)
- Budget type (required, radio buttons: Fixed Amount/Percentage)
- Budget value (required, decimal input with currency/percentage display)
- Account restrictions (optional, multi-select dropdown with search)

#### Budget Edit Dialog

**Form Fields:**

- Same as creation form, but with current values pre-filled
- All fields remain editable except period (requires explanation)
- Archive/unarchive toggle

## 🔄 User Workflows

### Creating a Budget

1. User clicks "Add Budget" button on budgets page
2. Budget creation dialog opens
3. User enters budget name and optional description
4. User selects budget period (defaults to month)
5. User chooses budget type (fixed amount or percentage)
6. User enters budget value with appropriate currency/percentage input
7. User optionally selects account restrictions
8. User submits form
9. Budget is created with first budget record for current period
10. Success toast notification is shown

### Viewing Budget Progress

1. User navigates to budgets page
2. Budget cards display current period progress
3. User can click on budget card to view detailed information
4. Budget details page shows comprehensive progress and history
5. User can navigate through historical budget records

### Managing Budgets

1. User can edit budget settings from list or details view
2. User can archive/unarchive budgets to organize active vs inactive
3. User can delete budgets (with confirmation dialog)
4. Changes are reflected immediately in the UI

## 🎨 Design Considerations

### Visual Hierarchy

- Clear budget status indicators with intuitive color coding
- Prominent progress visualization for quick assessment
- Budget type and period badges for easy identification
- Account restriction indicators when applicable

### Responsive Design

- Mobile-first approach with touch-friendly interactions
- Optimized card layouts for different screen sizes
- Simplified mobile view focusing on essential information
- Accessible progress indicators and controls

### Accessibility

- Proper ARIA labels and roles for progress indicators
- Keyboard navigation support
- Screen reader friendly progress descriptions
- High contrast color options for progress states
- Clear visual distinction between budget types and periods

## 🔗 Integration Points

### Navigation

- Add "Budgets" link to main navigation header (positioned after "Transactions")
- Update dashboard to show budget summary widgets
- Add budget-related quick actions to account pages

### Account Integration

- Budget creation allows account restrictions
- Account-based budget filtering and organization
- Account balance considerations for budget calculations

### Transaction Integration

- Budget usage automatically calculated from transaction data
- Transaction creation could show budget impact (future enhancement)
- Category-based budget tracking (future enhancement)

## 📱 Implementation Phases

### Phase 1: Core Functionality (Current Scope)

- Budgets list page with active/archived toggle
- Budget creation and editing dialogs
- Budget details page with current period information
- CRUD operations with proper error handling
- Basic budget records display with pagination
- Integration with accounts system

### Phase 2: Enhanced Features (Future)

- Advanced budget analytics and insights
- Budget alerts and notifications
- Category-based budget restrictions
- Budget templates and recurring budgets
- Enhanced mobile experience
- Budget vs actual spending reports

### Phase 3: Advanced Analytics (Future)

- Budget performance trends and forecasting
- Spending pattern analysis
- Budget optimization suggestions
- Integration with transaction analytics
- Advanced reporting and export features

## ✅ Acceptance Criteria

- [ ] Users can view list of active and archived budgets
- [ ] Users can create budgets with all supported periods and types
- [ ] Users can edit and delete existing budgets
- [ ] Users can view detailed budget information and progress
- [ ] Users can archive/unarchive budgets for organization
- [ ] Budget forms include proper validation and error handling
- [ ] UI clearly shows budget progress with visual indicators
- [ ] Budget records pagination works correctly
- [ ] Integration with accounts system works seamlessly
- [ ] UI is responsive and accessible
- [ ] Proper loading states and error handling
- [ ] Success/error notifications for all operations
- [ ] Account restrictions work correctly when specified

## 🚀 Future Enhancements

- Category-based budget restrictions and tracking
- Budget alerts and notifications (email, push, in-app)
- Budget templates for quick setup
- Recurring budget creation and management
- Advanced budget analytics and forecasting
- Budget vs actual spending detailed reports
- Budget sharing and collaboration features
- Integration with external financial planning tools
- Automated budget adjustments based on spending patterns
- Budget goal setting and achievement tracking
- Multi-currency budget support with conversion tracking
